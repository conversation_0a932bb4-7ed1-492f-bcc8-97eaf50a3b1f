import { getError } from "../tools/errors";
import { Queue, Worker } from 'bullmq';
import { 
    processCategory,
    processMasterData,
    processPriceList,
    processStock
} from '../roly';
import { excecute } from '../services/processors';

export const dataProcessingQueue = new Queue('data-processing-roly', {
    connection: {
        host: useRuntimeConfig().redis.host,
        port: useRuntimeConfig().redis.port,
        password: useRuntimeConfig().redis.password
    }
});

const worker = new Worker('data-processing-roly', async (job) => {
    const { origin } = job.data as { origin: string };

    try {
        console.info(`#### Worker data-processing-roly for ${origin} started. ####`);
        switch (origin) {
            case 'category':
                await processCategory();
            break;
            case 'master':
                await processMasterData();
            break;
            case 'price-list':
                await processPriceList();
            break;
            case 'stock':
                await processStock();
            break;
        }
        console.info(`#### Worker data-processing-roly for ${origin} finished. ####`);
    } catch (err: any) {
        console.error(`Error en el job ${job.id}, process ${origin}:`, err);
        throw getError(err);
    }
}, {
    connection: {
        host: useRuntimeConfig().redis.host,
        port: useRuntimeConfig().redis.port,
        password: useRuntimeConfig().redis.password
    },
    lockDuration: 30000
});

worker.on('completed', (job: any) => {
    console.info(`Job ${job.id} completed, process ${job.data.origin}.`);
});

worker.on('failed', (job: any, err: any) => {
    console.error(`Job ${job.id} failed, process ${job.data.origin}: ${err?.message}`);
    throw err;
});

export const allProcessorsQueue = new Queue('all-processors-roly', {
    connection: {
        host: useRuntimeConfig().redis.host,
        port: useRuntimeConfig().redis.port,
        password: useRuntimeConfig().redis.password
    }
});

const workerAllProcesor = new Worker('all-processors-roly', async (job) => {
    const { processor, data, session } = job.data;
    await excecute(processor, data, session);
}, {
    connection: {
        host: useRuntimeConfig().redis.host,
        port: useRuntimeConfig().redis.port,
        password: useRuntimeConfig().redis.password
    },
    lockDuration: 30000
});

workerAllProcesor.on('completed', (job: any) => {
    console.info(`Job ${job.id} completed.`);
});

workerAllProcesor.on('failed', async (job: any, err: any) => {
    console.error(`Job ${ job.id } failed: ${ err?.message } - ${ JSON.stringify(job.data) }`);
});
