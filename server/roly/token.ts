interface LoginResponse {
    token: string;
}

export async function getToken() {
    const config = useRuntimeConfig()
    const { url, login, user, password } = config.roly

    try {
        const formData = new FormData()
        formData.append('username', user)
        formData.append('password', password)

        const response = await $fetch<LoginResponse>(`${url}${login}`, {
            method: 'POST',
            body: formData
        });

        if ('token' in response) {
            return response.token;
        } else {
            throw new Error('error.tokenNotFound')
        }
    } catch (error) {
        console.error('Error getting token:', error)
        throw new Error('error.tokenFetchFailed')
    }
}
