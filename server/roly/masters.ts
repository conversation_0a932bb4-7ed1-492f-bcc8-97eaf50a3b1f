import { generateTokens } from "../connect/hasura/tools/auth";
import { useGraphQL, GraphQLServiceError } from "../connect/hasura/graphqlService";
import { processCatalog } from "./catalog";

interface SyncPayload {
    id?: string;
    code: string;
    name: string;
    hexcode?: string | null;
    comphexcode?: string | null;
    url?: string | null;
}

async function syncTable(
    tableName: string,
    processedData: any[],
    accessToken: string
) {
    const currentCodes = processedData.map((item: any) => item.id);

    try {
        await useGraphQL().insert(tableName, processedData, {
            constraint: `${tableName}_pkey`,
            update_columns: ['active'],
            where: { active: { _eq: false } }
        }, 'affected_rows', accessToken);
    } catch (err) {
        console.error(`Error inserting into ${tableName}:`, err);
    }

    if (tableName !== 'roly_product' && tableName !== 'roly_product_variant') {
        try {
            await useGraphQL().update(tableName, {
                active: false
            }, {
                id: { _nin: currentCodes },
                active: { _eq: true }
            }, 'affected_rows', accessToken);
        } catch (err) {
            console.error(`Error updating ${tableName}:`, err);
        }
    }
}

async function syncCatalogToHasura(processedData: any, accessToken: string) {
    try {
        // Sync families
        await syncTable(
            'roly_family',
            processedData.families
            .filter((item: any) => item.code != null && item.code !== '')
            .map((f: any) => ({
                id: f.code,
                name: f.name
            })),
            accessToken
        );

        // Sync sizes
        await syncTable(
            'roly_size',
            processedData.sizes
            .filter((item: any) => item.code != null && item.code !== '')
            .map((s: any) => ({
                id: s.code,
                name: s.name
            })),
            accessToken
        );

        // Sync colors
        await syncTable(
            'roly_color',
            processedData.colors
            .filter((item: SyncPayload) => item.code != null && item.code !== '')
            .map((c: SyncPayload) => ({
                id: c.code,
                name: c.name,
                hexcode: c.hexcode,
                comphexcode: c.comphexcode,
                url: c.url
            })),
            accessToken
        );

        // Sync products - Process sequentially to avoid foreign key violations
        for (const p of processedData.products) {
            try {
                // ----------------------------------------------------------
                let variants = p.variants.map((v: any) => ({ ...v }));
                for (const v of variants) {
                    v.product_id = p.id;
                }
                delete p.variants;
                // ----------------------------------------------------------
                let categories = [];
                if (p.categories?.length) {
                    const { roly_category } = await useGraphQL().get('roly_category', {
                        select: 'id',
                        where: { id: { _in: p.categories } }
                    }, accessToken);
                    categories = roly_category;
                }
                delete p.categories;

                const id = p.id;
                // ----------------------------------------------------------


                // ------------- Products -------------
                await syncTable('roly_product', [p], accessToken);
                // ----------- End Products -----------

                // ------------- Variants -------------
                if (variants && variants.length > 0) {
                    try {
                        await syncTable('roly_product_variant', variants, accessToken);
                    } catch (err) {
                        console.error('Error inserting product variants:', err);
                    }
                }
                // ----------- End Variants -----------

                // ------------- Categories -------------
                if (categories && categories.length > 0) {
                    const product_category = categories.map((c: { id: string }) => ({
                        product_id: id, category_id: c.id
                    }));

                    try {
                        await useGraphQL().insert(
                            'roly_product_category',
                            product_category,
                            {
                                constraint: 'roly_product_category_product_id_category_id_key',
                                update_columns: []
                            },
                            'affected_rows',
                            accessToken
                        )
                    } catch (err) {
                        console.error('Error inserting product categories:', err);
                    }
                }
                // ----------- End Categories -----------
            } catch (err) {
                console.error(`Error processing product ${p.id}:`, err);
            }
        }
    } catch (err) {
        console.error('Error in synchronization:', err);
        throw new GraphQLServiceError('error.syncFailed');
    }
}

export async function processMasterData() {
    try {
        const { accessToken } = await generateTokens('system', 'system');
        const processed = await processCatalog();
        await syncCatalogToHasura(processed, accessToken);
        return { status: 'success' };
    } catch (err) {
        throw err;
    }
}
