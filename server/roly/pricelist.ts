import { getToken } from "./token";
import { useGraphQL, GraphQLServiceError } from "../connect/hasura/graphqlService";
import { generateTokens } from "../connect/hasura/tools/auth";

async function syncPriceListToHasura(pricelist: any[]) {
    try {
        const { accessToken } = await generateTokens('system', 'system');
        await useGraphQL().updateMany('roly_product_variant', pricelist, 'affected_rows', accessToken);
    } catch (err) {
        console.error('Error in synchronization:', err);
        throw new GraphQLServiceError('error.syncFailed');
    }
}

export async function processPriceList() {
    const config = useRuntimeConfig();
    const { url, price } = config.roly;
    const brand = 'roly_stamina';
    
    try {
        const token = await getToken();
        const formData = new FormData()
        formData.append('brand', brand)

        const data: { pricelist: any[] } = await $fetch(`${url}${price}`, {
            headers: { 
                'Accept-Encoding': 'gzip, deflate',
                'Authorization': `Bearer ${token}`
            },
            method: 'POST',
            body: formData
        });

        const pricelist = data.pricelist.map((item: any) => ({
            where: { id: { _eq: item.productcode } },
            _set: {
                packincr: item.packincr ?? 0,
                unitsincr: item.unitsincr ?? 0,
                price_box: item.price_box ?? 0,
                price_pack: item.price_pack ?? 0,
                price_unit: item.price_unit ?? 0,
                price_box_conf: item.price_box_conf ?? 0,
                price_pack_conf: item.price_pack_conf ?? 0,
                price_unit_conf: item.price_unit_conf ?? 0,
                price_box_pvp: (item.price_box_pvp) ? item.price_box_pvp : (item.price_box ?? 0) * 2,
                price_pack_pvp: (item.price_pack_pvp) ? item.price_pack_pvp : (item.price_pack ?? 0) * 2,
                price_unit_pvp: (item.price_unit_pvp) ? item.price_unit_pvp : (item.price_unit ?? 0) * 2
            }
        }));

        await syncPriceListToHasura(pricelist);

        return { status: 'success' };
    } catch (err) {
        console.error('Error processing price list:', err);
        throw err;
    }
}
