import { getToken } from "./token";
import { useGraphQL, GraphQLServiceError } from "../connect/hasura/graphqlService";
import { generateTokens } from "../connect/hasura/tools/auth";

async function syncStockToHasura(stock: any[]) {
    try {
        const { accessToken } = await generateTokens('system', 'system');
        await useGraphQL().updateMany('roly_product_variant', stock, 'affected_rows', accessToken);
    } catch (err) {
        console.error('Error in synchronization:', err);
        throw new GraphQLServiceError('error.syncFailed');
    }
}

export async function processStock() {
    const config = useRuntimeConfig();
    const { url, stock } = config.roly;
    const whscode = '01';
    const brand = 'roly_stamina';

    try {
        const token = await getToken();
        const formData = new FormData()
        formData.append('brand', brand)
        formData.append('whscode', whscode)

        const data: { stock: any[] } = await $fetch(`${url}${stock}`, {
            headers: { 
                'Accept-Encoding': 'gzip, deflate',
                'Authorization': `Bearer ${token}`
            },
            method: 'POST',
            body: formData
        });

        const stockData = data.stock.map((item: any) => ({
            where: { id: { _eq: item.sku } },
            _set: {
                onhand: item.onhand,
                incoming: item.incoming,
                state: item.state,
                canteco: item.canteco
            }
        }));

        await syncStockToHasura(stockData);

        return { status: 'success' };
    } catch (err) {
        console.error('Error processing stock:', err);
        throw err;
    }
}
