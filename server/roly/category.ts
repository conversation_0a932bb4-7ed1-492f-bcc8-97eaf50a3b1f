import { getToken } from "./token";
import { useGraphQL, GraphQLServiceError } from "../connect/hasura/graphqlService";
import { generateSlug } from "../tools/utils";
import { generateTokens } from "../connect/hasura/tools/auth";

interface SyncPayload {
    id: string;
    name: string;
    parent_id: string;
    order: number;
    slug: string;
    active?: boolean;
}

async function syncCategoryToHasura(categories: SyncPayload[]) {
    try {
        const { accessToken } = await generateTokens('system', 'system');

        await useGraphQL().insert('roly_category', categories, {
            constraint: 'roly_category_pkey',
            update_columns: ['active'],
            where: { active: { _eq: false } }
        }, 'affected_rows', accessToken);

        await useGraphQL().update('roly_category', {
            active: false
        }, {
            id: {_nin: categories.map((c: SyncPayload) => c.id)},
            active: { _eq: true }
        }, 'affected_rows', accessToken);
    } catch (err) {
        console.error('Error in synchronization:', err);
        throw new GraphQLServiceError('error.syncFailed');
    }
}

export async function processCategory() {
    const config = useRuntimeConfig()
    const { url, category } = config.roly

    let token: string;
    const lang = 'es-ES'
    const brand = 'roly_stamina'

    try {
        token = await getToken();
    } catch (error) {
        console.error('Error getting token:', error);
        throw new Error('error.tokenFetchFailed');
    }

    try {
        const data: any[] = await $fetch(`${url}${category}`, {
            headers: { 
                'Accept-Encoding': 'gzip, deflate',
                'Authorization': `Bearer ${token}`
            },
            method: 'GET',
            params: {
                lang: lang,
                brand: brand
            }
        });

        const categories: SyncPayload[] = data.map((item: any) => ({
            id: item.id,
            name: item.category,
            parent_id: item?.parentId || null,
            order: parseInt(item.branchOrder.split('\\')[0]) || 0,
            slug: `${generateSlug(item.category)}-${generateSlug(item.id)}`
        }));

        await syncCategoryToHasura(categories);

        return { status: 'success' };
    } catch (err) {
        console.error('Error processing category:', err);
        throw err;
    }
}
