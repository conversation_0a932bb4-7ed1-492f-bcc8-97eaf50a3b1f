import { getToken } from "./token";
import { removeAccents } from "../tools/utils";

interface Images {
    url: string;
}

interface Variant {
    id: string;
    itemname: string;
    name_normalized: string;
    eancode: string;
    eanpack: string;
    eanbox: string;
    measures: string;
    size_id: string;
    color_id: string;
    productimage: string;
    viewsimages: string;
    otherimages: string;
    boxsize: string;
    weight: string;
    weightunit: string;
    isforchildren: boolean;
    active?: boolean;
}

interface Product {
    id: string;
    modelcode: string;
    modelname: string;
    description: string;
    description_normalized: string;
    composition: string;
    observations: string;
    moq: number;
    stepqty: number;
    packunits: number;
    boxunits: number;
    family_id: string;
    gender_id: string;
    modelimage: string;
    childimage: string;
    detailsimages: string;
    isforchildren: boolean;
    isnovelty: boolean;
    isoutlet: number;
    packincr: number;
    unitsincr: number;
    taric: string;
    madein: string;
    canondigital: number;
    categoriesids: string;
    allimages: Images[];
    active?: boolean;
    variants?: Variant[];
    categories?: string[];
}

interface ProcessedData {
    families: Array<{ code: string; name: string }>;
    sizes: Array<{ code: string; name: string }>;
    colors: Array<{ code: string; name: string; hexcode: string; comphexcode: string; url: string }>;
    products: Product[];
}

export async function processCatalog() {
    const config = useRuntimeConfig()
    const { url, catalog } = config.roly

    let token: string;
    const lang = 'es-ES'
    const brand = 'roly_stamina'

    try {
        token = await getToken();
    } catch (error) {
        console.error('Error getting token:', error);
        throw new Error('error.tokenFetchFailed');
    }

    try {
        const data: { item: any[] } = await $fetch(`${url}${catalog}`, {
            headers: { 
                'Accept-Encoding': 'gzip, deflate',
                'Authorization': `Bearer ${token}`
            },
            method: 'GET',
            params: { lang, brand }
        });

        if (!data?.item || data.item.length === 0) {
            throw new Error('error.emptyCatalog');
        }

        const processed: ProcessedData = {
            families: [],
            sizes: [],
            colors: [],
            products: []
        };

        const seen = {
            families: new Set<string>(),
            sizes: new Set<string>(),
            colors: new Set<string>(),
            products: new Map<string, Product>()
        };

        for (const item of data.item) {
            if (item.brand === 'roly_stamina' || item.brand === 'roly') {
                // Families processing
                if (!seen.families.has(item.familycode)) {
                    seen.families.add(item.familycode);
                    processed.families.push({
                        code: item.familycode,
                        name: item.family
                    });
                }

                // Sizes processing
                if (!seen.sizes.has(item.sizecode)) {
                    seen.sizes.add(item.sizecode);
                    processed.sizes.push({
                        code: item.sizecode,
                        name: item.sizename
                    });
                }

                // Colors processing
                if (item.colordata && !seen.colors.has(item.colorcode)) {
                    seen.colors.add(item.colorcode);
                    processed.colors.push({
                        code: item.colorcode,
                        name: item.colorname,
                        hexcode: item.colordata?.hexcode ?? null,
                        comphexcode: item.colordata?.comphexcode ?? null,
                        url: item.colordata?.url ?? null
                    });
                }

                // Products processing
                if (item.itemname && item.itemname !== '') {
                    if (!seen.products.has(item.modelid)) {
                        seen.products.set(item.modelid, {
                            id: item.modelid,
                            modelcode: item.modelcode,
                            modelname: item.modelname,
                            description: item.description,
                            description_normalized: removeAccents(item.description),
                            composition: item.composition,
                            observations: item.observations,
                            moq: Number(item.moq),
                            stepqty: Number(item.stepqty),
                            packunits: Number(item.packunits),
                            boxunits: Number(item.boxunits),
                            family_id: item.familycode,
                            gender_id: item.gendercode,
                            modelimage: item.modelimage,
                            childimage: item.childimage,
                            detailsimages: item.detailsimages ?? null,
                            isforchildren: false,
                            isnovelty: item.isnovelty === "True",
                            isoutlet: Number(item.isoutlet),
                            packincr: Number(item.packincr),
                            unitsincr: Number(item.unitsincr),
                            taric: item.taric,
                            madein: item.madein,
                            canondigital: Number(item.canondigital),
                            categoriesids: item.categoriesids ?? null,
                            allimages: [
                                { url: item.modelimage },
                                { url: item.childimage },
                                // ...((item.viewsimages ?? '').split(',').filter(Boolean).map((url: string) => ({ url }))),
                                ...((item.detailsimages ?? '').split(',').filter(Boolean).map((url: string) => ({ url }))),
                                ...((item.otherimages ?? '').split(',').filter(Boolean).map((url: string) => ({ url })))
                            ].filter(item => Boolean(item.url)),
                            variants: [],
                            categories: item.categoriesids?.split(',') ?? [],
                            active: true
                        });
                    }

                    const product: any = seen.products.get(item.modelid);
                    product?.variants.push({
                        id: item.itemcode,
                        itemname: item.itemname,
                        name_normalized: removeAccents(item.itemname),
                        eancode: item.eancode,
                        eanpack: item.eanpack,
                        eanbox: item.eanbox,
                        measures: item.measures,
                        size_id: item.sizecode,
                        color_id: item.colorcode,
                        productimage: item.productimage,
                        viewsimages: item.viewsimages ?? null,
                        otherimages: item.otherimages ?? null,
                        boxsize: item.boxsize,
                        weight: item.weight,
                        weightunit: item.weightunit,
                        isforchildren: item.isforchildren === "True" || item.isforchildren === "1" || item.isforchildren === "true",
                        active: true
                    });

                    if (product?.variants.some((variant: any) => variant.isforchildren === true)) {
                        product.isforchildren = true;
                    }
                }
            }
        }
        processed.products = Array.from(seen.products.values());

        return processed;
    } catch (err) {
        console.error('Error processing catalog:', err);
        throw new Error('error.processingCatalog');
    }
}
