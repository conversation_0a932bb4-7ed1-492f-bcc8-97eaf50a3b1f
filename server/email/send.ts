// @ts-ignore
import hogan from "hogan.js";
import fs from "fs";
import { MailOptions } from "./interface/email.interface";

export const sendEmail = (mailOptions: MailOptions, template:string, data: any = {}) => {
    const { sendMail } = useNodeMailer();
    const pathToTemplate = `server/email/templates/${ template }.hjs`;

    try {
        const templateFile = fs.readFileSync(pathToTemplate, 'utf8');
        let compiledtemplate = hogan.compile(templateFile);
        mailOptions.html = compiledtemplate.render(data);

        sendMail(mailOptions);
    } catch (err: any) {
        console.error(`Error sending email ${ template }:`, err);
    }
}
