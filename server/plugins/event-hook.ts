import eventBus from '../tools/eventBus';
import { sendEmail } from "../email/send";
import { MailOptions } from "../email/interface/email.interface";
import { DatabaseInterface } from "../connect/interface/database.interface";
import { DatabaseFactory } from "../connect/factory/database.factory";

export default defineNitroPlugin(async (nitroApp) => {
    eventBus.on('auth:register', (data) => {
        if (useRuntimeConfig().auth.register.verifyRequired) {
            const mailOptions: MailOptions = {
                subject: '¡Tu código de verificación para EBP Publicidad!',
                to: data.email,
            }

            sendEmail(mailOptions, 'send_code', { code: data.email_code_verify });
        }

        console.info('Captured event auth:register:', data);
    });

    eventBus.on('auth:updateProfile', (data) => {
        console.info('Captured event auth:updateProfile:', data);
    });

    eventBus.on('auth:login', (data) => {
        console.info('Captured event auth:login:', data);
    });

    eventBus.on('auth:forgotPassword', (data) => {
        if (data.email && data.codeRecovery) {
            const mailOptions: MailOptions = {
                subject: '¡Tu código de verificación para EBP Publicidad!',
                to: data.email,
            }

            sendEmail(mailOptions, 'send_code_forgot', { code: data.codeRecovery });
        }

        console.info('Captured event auth:forgotPassword:', data);
    });

    eventBus.on('auth:updatePassword', (data) => {
        console.info('Captured event auth:updatePassword:', data);
    });

    eventBus.on('new:sale', ({ data, session }) => {
        if(session?.user?.email === data.email_customer) {
            if (data.email_customer) {
                const mailOptions: MailOptions = {
                    subject: '¡Tu orden de EBP Publicidad ha sido recibida!',
                    to: data.email_customer,
                }

                sendEmail(mailOptions, 'thank', data);
            }

            if (data.notify_email?.length > 0) {
                data.notify_email.forEach((item: any) => {
                    const mailOptions: MailOptions = {
                        subject: '¡Nueva orden en EBP Publicidad!',
                        to: item.email,
                    }

                    sendEmail(mailOptions, 'new_sale', data);
                });
            }
        }

        console.info('Captured event new:sale:', data);
    });

    eventBus.on('error:xml', async (data) => {
        const db: DatabaseInterface = DatabaseFactory.getDatabase();
        const { roly_config: config } = await db.getConfig();

        if (config[0].notify?.length > 0) {
            config[0].notify.forEach((item: any) => {
                const mailOptions: MailOptions = {
                    subject: 'Nuevo error de EBP Publicidad',
                    to: item.email,
                }

                sendEmail(mailOptions, 'error', data);
            });
        }

        console.info('Captured event error:', data);
    });

    eventBus.on('info:xml', async (data) => {
        const db: DatabaseInterface = DatabaseFactory.getDatabase();
        const { roly_config: config } = await db.getConfig();

        if (config[0].notify?.length > 0) {
            config[0].notify.forEach((item: any) => {
                const mailOptions: MailOptions = {
                    subject: 'Nueva información de EBP Publicidad',
                    to: item.email,
                }

                sendEmail(mailOptions, 'info', data);
            });
        }

        console.info('Captured event info:', data);
    });
});
