import { DatabaseInterface } from "../../../connect/interface/database.interface";
import { DatabaseFactory } from "../../../connect/factory/database.factory";
import { getError } from "../../../tools/errors";

export default defineEventHandler(async (event) => {
    try {
        const db: DatabaseInterface = DatabaseFactory.getDatabase();
        const { roly_config: config } = await db.getConfig();
        return { data: { config }};
    } catch (err: any) {
        throw getError(err);
    }
})
