import { allProcessorsQueue, dataProcessingQueue } from '../../../../queues/processor';

export default defineEventHandler(async (event) => {
    // Verificar autenticación/autorización
    const key = event.node.req.headers['key'];
    const config = useRuntimeConfig();

    if (!key || key !== config.hasura.verifyKey) {
        console.error('Invalid key:', key);
        throw createError({
            statusCode: 403,
            message: 'queue.error.invalidKey',
        });
    }

    try {
        console.info('#### Getting queue status ####');
        
        // Obtener estadísticas de ambas colas
        const [allProcessorsStats, xmlProcessingStats] = await Promise.all([
            allProcessorsQueue.getJobCounts(),
            dataProcessingQueue.getJobCounts()
        ]);

        // Obtener información adicional sobre las colas
        const [allProcessorsWaiting, xmlProcessingWaiting] = await Promise.all([
            allProcessorsQueue.getWaiting(0, 10), // Primeros 10 jobs en espera
            dataProcessingQueue.getWaiting(0, 10)
        ]);

        const [allProcessorsActive, xmlProcessingActive] = await Promise.all([
            allProcessorsQueue.getActive(0, 10), // Primeros 10 jobs activos
            dataProcessingQueue.getActive(0, 10)
        ]);

        const [allProcessorsFailed, xmlProcessingFailed] = await Promise.all([
            allProcessorsQueue.getFailed(0, 10), // Primeros 10 jobs fallidos
            dataProcessingQueue.getFailed(0, 10)
        ]);

        const queueStatus = {
            timestamp: new Date().toISOString(),
            queues: {
                'all-processors-roly': {
                    name: 'all-processors-roly',
                    stats: allProcessorsStats,
                    samples: {
                        waiting: allProcessorsWaiting.map(job => ({
                            id: job.id,
                            name: job.name,
                            data: job.data,
                            timestamp: job.timestamp,
                            delay: job.delay
                        })),
                        active: allProcessorsActive.map(job => ({
                            id: job.id,
                            name: job.name,
                            data: job.data,
                            timestamp: job.timestamp,
                            processedOn: job.processedOn
                        })),
                        failed: allProcessorsFailed.map(job => ({
                            id: job.id,
                            name: job.name,
                            data: job.data,
                            failedReason: job.failedReason,
                            timestamp: job.timestamp
                        }))
                    }
                },
                'data-processing-roly': {
                    name: 'data-processing-roly',
                    stats: xmlProcessingStats,
                    samples: {
                        waiting: xmlProcessingWaiting.map(job => ({
                            id: job.id,
                            name: job.name,
                            data: job.data,
                            timestamp: job.timestamp,
                            delay: job.delay
                        })),
                        active: xmlProcessingActive.map(job => ({
                            id: job.id,
                            name: job.name,
                            data: job.data,
                            timestamp: job.timestamp,
                            processedOn: job.processedOn
                        })),
                        failed: xmlProcessingFailed.map(job => ({
                            id: job.id,
                            name: job.name,
                            data: job.data,
                            failedReason: job.failedReason,
                            timestamp: job.timestamp
                        }))
                    }
                }
            },
            summary: {
                totalJobs: (allProcessorsStats.waiting || 0) + (allProcessorsStats.active || 0) + 
                          (allProcessorsStats.completed || 0) + (allProcessorsStats.failed || 0) + 
                          (allProcessorsStats.delayed || 0) + (allProcessorsStats.paused || 0) +
                          (xmlProcessingStats.waiting || 0) + (xmlProcessingStats.active || 0) + 
                          (xmlProcessingStats.completed || 0) + (xmlProcessingStats.failed || 0) + 
                          (xmlProcessingStats.delayed || 0) + (xmlProcessingStats.paused || 0),
                totalActive: (allProcessorsStats.active || 0) + (xmlProcessingStats.active || 0),
                totalWaiting: (allProcessorsStats.waiting || 0) + (xmlProcessingStats.waiting || 0),
                totalFailed: (allProcessorsStats.failed || 0) + (xmlProcessingStats.failed || 0),
                totalCompleted: (allProcessorsStats.completed || 0) + (xmlProcessingStats.completed || 0)
            }
        };

        console.info('Queue status retrieved successfully');
        
        return queueStatus;
        
    } catch (err: any) {
        console.error('Error getting queue status:', err);
        throw createError({
            statusCode: 500,
            message: 'queue.error.gettingStatus',
            data: {
                error: err.message
            }
        });
    }
});
