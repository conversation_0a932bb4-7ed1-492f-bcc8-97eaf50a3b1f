import { allProcessorsQueue, dataProcessingQueue } from '../../../../queues/processor';

// Force cleanup Redis keys when normal methods fail
async function forceCleanRedisKeys(queue: any, queueName: string): Promise<void> {
    try {
        console.info(`Attempting force Redis cleanup for queue: ${queueName}`);

        // Get the Redis connection from the queue
        const client = await queue.client;

        // Define key patterns for BullMQ queues
        const keyPatterns = [
            `bull:${queueName}:*`,
            `bull:${queueName}:active`,
            `bull:${queueName}:wait`,
            `bull:${queueName}:paused`,
            `bull:${queueName}:delayed`,
            `bull:${queueName}:failed`,
            `bull:${queueName}:completed`,
            `bull:${queueName}:stalled-check`,
            `bull:${queueName}:events`,
            `bull:${queueName}:id`
        ];

        // Delete keys matching the patterns
        for (const pattern of keyPatterns) {
            try {
                const keys = await client.keys(pattern);
                if (keys.length > 0) {
                    await client.del(...keys);
                    console.info(`Deleted ${keys.length} Redis keys for pattern: ${pattern}`);
                }
            } catch (patternError: any) {
                console.warn(`Could not delete keys for pattern ${pattern}:`, patternError.message);
            }
        }

        console.info(`Force Redis cleanup completed for queue: ${queueName}`);
    } catch (error: any) {
        console.error(`Force Redis cleanup failed for queue ${queueName}:`, error.message);
        throw error;
    }
}

export default defineEventHandler(async (event) => {
    // Verificar autenticación/autorización
    const key = event.node.req.headers['key'];
    const config = useRuntimeConfig();

    if (!key || key !== config.hasura.verifyKey) {
        console.error('Invalid key:', key);
        throw createError({
            statusCode: 403,
            message: 'queue.error.invalidKey',
        });
    }

    try {
        console.info('#### Starting queue cleanup process ####');

        // Obtener estadísticas antes de limpiar
        const allProcessorsStats = await allProcessorsQueue.getJobCounts();
        const xmlProcessingStats = await dataProcessingQueue.getJobCounts();

        console.info('Queue stats before cleanup:', {
            allProcessors: allProcessorsStats,
            xmlProcessing: xmlProcessingStats
        });

        const cleanupResults: any = {
            allProcessors: { success: false, method: '', error: null },
            xmlProcessing: { success: false, method: '', error: null }
        };

        // Enhanced function to safely clear a queue with better handling of stuck jobs
        const clearQueueSafely = async (queue: any, queueName: string) => {
            try {
                console.info(`Starting cleanup for queue: ${queueName}`);
                const stats = await queue.getJobCounts();
                console.info(`Queue ${queueName} stats:`, stats);

                if (stats.active === 0) {
                    // No active jobs, safe to obliterate
                    await queue.obliterate();
                    console.info(`Queue ${queueName} obliterated successfully`);
                    return { success: true, method: 'obliterate', error: null };
                } else {
                    // Active jobs detected - use aggressive cleanup strategy
                    console.warn(`Queue ${queueName} has ${stats.active} active jobs. Using aggressive cleanup method.`);

                    // Step 1: Pause the queue to prevent new job processing
                    await queue.pause();
                    console.info(`Queue ${queueName} paused`);

                    // Step 2: Clean non-active jobs first
                    const jobTypes = ['completed', 'failed', 'waiting', 'delayed', 'paused'];
                    const cleanResults: any = {};

                    for (const jobType of jobTypes) {
                        try {
                            const deleted = await queue.clean(0, 0, jobType);
                            cleanResults[jobType] = deleted.length;
                        } catch (cleanError: any) {
                            console.warn(`Error cleaning ${jobType} jobs from ${queueName}:`, cleanError.message);
                            cleanResults[jobType] = `Error: ${cleanError.message}`;
                        }
                    }

                    // Step 3: Handle active jobs with multiple strategies
                    try {
                        const activeJobs = await queue.getActive();
                        let activeCleared = 0;
                        let forceCleared = 0;

                        console.info(`Found ${activeJobs.length} active jobs in ${queueName}`);

                        for (const job of activeJobs) {
                            try {
                                console.info(`Attempting to clear active job ${job.id}`);

                                // Strategy 1: Try to move to failed state
                                await job.moveToFailed(new Error('Queue cleared by admin'), 'admin-clear', false);
                                activeCleared++;
                                console.info(`Job ${job.id} moved to failed state`);

                            } catch (jobError: any) {
                                console.warn(`Could not move job ${job.id} to failed:`, jobError.message);

                                try {
                                    // Strategy 2: Force remove the job
                                    await job.remove();
                                    forceCleared++;
                                    console.info(`Job ${job.id} force removed`);

                                } catch (removeError: any) {
                                    console.error(`Could not remove job ${job.id}:`, removeError.message);

                                    try {
                                        // Strategy 3: Discard the job (most aggressive)
                                        await job.discard();
                                        forceCleared++;
                                        console.info(`Job ${job.id} discarded`);

                                    } catch (discardError: any) {
                                        console.error(`Could not discard job ${job.id}:`, discardError.message);
                                    }
                                }
                            }
                        }

                        cleanResults.active = {
                            total: activeJobs.length,
                            movedToFailed: activeCleared,
                            forceRemoved: forceCleared,
                            remaining: activeJobs.length - activeCleared - forceCleared
                        };

                    } catch (activeError: any) {
                        console.error(`Error handling active jobs in ${queueName}:`, activeError.message);
                        cleanResults.active = `Error: ${activeError.message}`;
                    }

                    // Step 4: Resume the queue
                    await queue.resume();
                    console.info(`Queue ${queueName} resumed`);

                    // Step 5: Wait a moment for any remaining operations to complete
                    await new Promise(resolve => setTimeout(resolve, 2000));

                    // Step 6: Try obliterate() again after cleanup
                    try {
                        await queue.obliterate();
                        console.info(`Queue ${queueName} obliterated after cleanup`);
                        return { success: true, method: 'clean + obliterate', error: null, details: cleanResults };
                    } catch (obliterateError: any) {
                        console.warn(`Could not obliterate ${queueName} after cleanup:`, obliterateError.message);

                        // Step 7: Last resort - direct Redis cleanup
                        try {
                            await forceCleanRedisKeys(queue, queueName);
                            return { success: true, method: 'clean + redis force', error: null, details: cleanResults };
                        } catch (redisError: any) {
                            console.error(`Redis force cleanup failed for ${queueName}:`, redisError.message);
                            return { success: false, method: 'clean only', error: obliterateError.message, details: cleanResults };
                        }
                    }
                }
            } catch (error: any) {
                return { success: false, method: 'failed', error: error.message };
            }
        };

        // Limpiar ambas colas
        cleanupResults.allProcessors = await clearQueueSafely(allProcessorsQueue, 'all-processors-roly');
        cleanupResults.xmlProcessing = await clearQueueSafely(dataProcessingQueue, 'data-processing-roly');

        // Obtener estadísticas después de la limpieza
        const allProcessorsStatsAfter = await allProcessorsQueue.getJobCounts();
        const xmlProcessingStatsAfter = await dataProcessingQueue.getJobCounts();

        console.info('Queue stats after cleanup:', {
            allProcessors: allProcessorsStatsAfter,
            xmlProcessing: xmlProcessingStatsAfter
        });

        console.info('#### Queue cleanup completed ####');

        const overallSuccess = cleanupResults.allProcessors.success && cleanupResults.xmlProcessing.success;

        return {
            success: overallSuccess,
            message: overallSuccess ? 'All Bull queues have been cleared successfully' : 'Queue cleanup completed with some issues',
            results: cleanupResults,
            statistics: {
                before: {
                    allProcessors: allProcessorsStats,
                    xmlProcessing: xmlProcessingStats
                },
                after: {
                    allProcessors: allProcessorsStatsAfter,
                    xmlProcessing: xmlProcessingStatsAfter
                }
            }
        };
        
    } catch (err: any) {
        console.error('Error clearing Bull queues:', err);
        throw createError({
            statusCode: 500,
            message: 'queue.error.clearingQueues',
            data: {
                error: err.message
            }
        });
    }
});
