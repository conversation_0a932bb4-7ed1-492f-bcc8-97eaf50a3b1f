import { getError } from "../../../../tools/errors";
import { dataProcessingQueue } from "../../../../queues/processor";

export default defineEventHandler(async (event) => {
    const key = event.node.req.headers['key'];
    const config = useRuntimeConfig();

    if (!key || key !== config.hasura.verifyKey) {
        console.error('Invalid key:', key);
        throw createError({
            statusCode: 403,
            message: 'store.error.invalidKey',
        });
    }

    try {
        await dataProcessingQueue.add('data-processing-roly', { origin: 'price-list' }, {
            attempts: 2, 
            removeOnComplete: true,
            removeOnFail: 1000
        });

        return { status: 'success', origin: 'price-list' };
    } catch (err) {
        throw getError(err);
    }
});
