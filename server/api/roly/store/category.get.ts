import { DatabaseInterface } from "../../../connect/interface/database.interface";
import { DatabaseFactory } from "../../../connect/factory/database.factory";
import { getError } from "../../../tools/errors";

export default defineEventHandler(async (event) => {
    try {
        const db: DatabaseInterface = DatabaseFactory.getDatabase();
        const { roly_category: categories } = await db.getCategory();
        return { data: { categories }};
    } catch (err: any) {
        throw getError(err);
    }
})
