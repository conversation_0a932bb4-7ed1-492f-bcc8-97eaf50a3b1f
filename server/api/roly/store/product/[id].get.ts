import { DatabaseInterface } from "../../../../connect/interface/database.interface";
import { DatabaseFactory } from "../../../../connect/factory/database.factory";
import { getError } from "../../../../tools/errors";

export default defineEventHandler(async (event) => {
    const id = event.context.params?.id as string || '';

    try {
        const db: DatabaseInterface = DatabaseFactory.getDatabase();
        const { roly_product_with_prices: product } = await db.getProduct(id);
        return { data: { product }};
    } catch (err: any) {
        throw getError(err);
    }
});
