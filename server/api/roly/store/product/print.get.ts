import { DatabaseInterface } from "../../../../connect/interface/database.interface";
import { DatabaseFactory } from "../../../../connect/factory/database.factory";
import { getError } from "../../../../tools/errors";

export default defineEventHandler(async (event) => {
    try {
        const db: DatabaseInterface = DatabaseFactory.getDatabase();
        const { roly_print: prints } = await db.getPrints();
        return { data: { prints }};
    } catch (err: any) {
        throw getError(err);
    }
});
