import { DatabaseInterface } from "../../../../connect/interface/database.interface";
import { DatabaseFactory } from "../../../../connect/factory/database.factory";
import { getError } from "../../../../tools/errors";

export default defineEventHandler(async (event) => {
    try {
        const body = await readBody(event);
        const data: string = body?.data as any;

        if (data) {
            const db: DatabaseInterface = DatabaseFactory.getDatabase();
            const { roly_product_with_prices: products, roly_product_with_prices_aggregate: total } = await db.getProducts(data);
            return { data: { products, total }};
        } else {
            console.error('Data is mandatory.');
            throw createError({
                statusCode: 403,
                message: 'store.error.dataMandatory',
            });
        }
    } catch (err: any) {
        throw getError(err);
    }
});
