import { AuthFactory } from "../../connect/factory/auth.factory";
import { AuthInterface } from "../../connect/interface/auth.interface";
import { registerPayload } from "../../connect/interface/database.interface";
import { generateRandomNumericCode, verifyToken } from "../../tools/utils";
import eventBus from '../../tools/eventBus';
import { createError } from 'h3';

export default defineEventHandler(async (event) => {
    await verifyToken(event);

    if (event.node.req.method !== 'POST') {
        throw createError({
            statusCode: 404,
            message: "error.notFound",
        });
    }

    const payload: registerPayload = await readBody(event);
    if (!payload.email || !payload.password) {
        throw createError({
            statusCode: 400,
            message: "error.emailPasswordRequired",
        });
    }

    if (useRuntimeConfig().auth.register.verifyRequired) {
        const code = generateRandomNumericCode(6);
        payload.email_code_verify = code;
    }

    try {
        const auth:AuthInterface = AuthFactory.getAuth();
        const { user } = await auth.register({ payload, strategy: 'local' });
        
        if (!user || !user.id) {
            throw createError({
                statusCode: 400,
                message: "error.registrationFailed",
            });
        }

        if (!useRuntimeConfig().auth.register.verifyRequired) {
            await setUserSession(event, {
                user,
                loggedInAt: new Date()
            });
        }

        // **** Event auth:register ****
        eventBus.emit('auth:register', user);

        return { data: { status: 'success' } };
    } catch (err: any) {
        if (err.message === "error.emailDuplicated") {
            throw createError({
                statusCode: 400,
                message: "error.emailAlreadyRegistered",
            });
        }

        throw createError({
            statusCode: err?.statusCode || 500,
            message: err?.message || "error.internalServerError",
        });
    }
});
