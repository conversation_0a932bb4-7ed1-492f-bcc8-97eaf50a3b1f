import { AuthInterface } from "../../../connect/interface/auth.interface";
import { AuthFactory } from "../../../connect/factory/auth.factory";
import { verifyToken } from "~~/server/tools/utils";

export default defineEventHandler(async (event) => {
    await verifyToken(event);

    try {
        const session: any = await getUserSession(event);
        const body = await readBody(event);
        const { id } = body;

        if (session.user?.id) {
            const db: AuthInterface = AuthFactory.getAuth();
            await db.deleteAddress({ payload: { id } , user: session.user });
            return true;
        } else {
            console.error('User not found.');
            throw createError({
                statusCode: 404,
                message: 'store.error.userNotFound',
            });
        }
    } catch (err: any) {
        console.error('Error deleting address:', err);
        throw createError({
            statusCode: 500,
            message: 'store.error.deletingAddress',
        });
    }
});
