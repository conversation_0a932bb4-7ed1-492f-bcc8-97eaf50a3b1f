import { AuthInterface } from "../../../connect/interface/auth.interface";
import { AuthFactory } from "../../../connect/factory/auth.factory";
import { verifyToken } from "~~/server/tools/utils";

export default defineEventHandler(async (event) => {
    await verifyToken(event);

    try {
        const session: any = await getUserSession(event);

        if (session.user?.id) {
            const db: AuthInterface = AuthFactory.getAuth();
            const { user } = await db.getAddresses({ payload: { id: session.user?.id } , user: session.user });
            return { data: { user }};
        } else {
            console.error('User not found.');
            throw createError({
                statusCode: 404,
                message: 'store.error.userNotFound',
            });
        }
    } catch (err: any) {
        console.error('Error getting addresses:', err);
        throw createError({
            statusCode: 500,
            message: 'store.error.gettingAddresses',
        });
    }
});
