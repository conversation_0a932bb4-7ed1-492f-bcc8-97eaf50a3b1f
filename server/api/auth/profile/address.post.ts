import { AuthInterface } from "../../../connect/interface/auth.interface";
import { AuthFactory } from "../../../connect/factory/auth.factory";
import { verifyToken } from "~~/server/tools/utils";

export default defineEventHandler(async (event) => {
    await verifyToken(event);

    try {
        const session: any = await getUserSession(event);
        const body = await readBody(event);

        if (session.user?.id) {
            const db: AuthInterface = AuthFactory.getAuth();
            const { insert_user_addresses: address } = await db.insertAddress({ payload: { data: body }, user: session.user });
            return { data: address };
        } else {
            console.error('User not found.');
            throw createError({
                statusCode: 404,
                message: 'store.error.userNotFound',
            });
        }
    } catch (err: any) {
        console.error('Error inserting address:', err);
        throw createError({
            statusCode: 500,
            message: 'store.error.insertingAddress',
        });
    }
});
