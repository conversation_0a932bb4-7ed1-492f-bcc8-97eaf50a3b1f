import { verifyToken } from "../../../server/tools/utils";
import { AuthFactory } from "../../connect/factory/auth.factory";
import { AuthInterface } from "../../connect/interface/auth.interface";
import eventBus from '../../tools/eventBus';
import { createError } from 'h3';

export default defineEventHandler(async (event) => {
    await verifyToken(event);
    
    if (event.node.req.method !== 'POST') {
        throw createError({
            statusCode: 404,
            message: "error.notFound",
        });
    }

    const { email, password } = await readBody(event);
    if (!email || !password) {
        throw createError({
            statusCode: 400,
            message: "error.emailPasswordRequired",
        });
    }

    try {
        const auth:AuthInterface = AuthFactory.getAuth();
        const { user } = await auth.login({ email, password, strategy: 'local' });

        if (!user || !user.id) {
            throw createError({
                statusCode: 400,
                message: "error.invalidCredentials",
            });
        }

        if (useRuntimeConfig().auth.register.verifyRequired && !user.email_verify) {
            // **** Event auth:register ****
            eventBus.emit('auth:register', user);
            return { data: { status: 'failure', reason: 'error.unverifiedEmail' } };
        }

        if (!user.roles || user.roles.length === 0) {
            return { data: { status: 'failure', reason: 'error.unauthorized' } };
        }

        await setUserSession(event, {
            user,
            loggedInAt: new Date()
        });

        // **** Event auth:login ****
        eventBus.emit('auth:login', { user_id: user.id, date: new Date() });

        return { data: { status: 'success' } };
    } catch (err: any) {
        throw createError({
            statusCode: err?.statusCode || 500,
            message: err?.message || "error.internalServerError",
        });
    }
});
