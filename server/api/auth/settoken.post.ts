import { getHashedPassword } from "../../connect/hasura/tools/auth";

export default defineEventHandler(async (event) => {
    const config = useRuntimeConfig();
    const token = await getHashedPassword(config.api.token.verify);

    setCookie(event, 'api-token', token, {
        httpOnly: true,
        secure: true,
        sameSite: 'strict',
        path: '/',
        maxAge: 60 * 60 * 24
    });

    return {
        message: 'Set api-token to <PERSON><PERSON> on server'
    }
});
