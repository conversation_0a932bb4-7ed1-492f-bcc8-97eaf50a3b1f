import { verifyToken } from "~~/server/tools/utils";
import { AuthFactory } from "../../../connect/factory/auth.factory";
import { AuthInterface } from "../../../connect/interface/auth.interface";
import { createError } from 'h3';

export default defineEventHandler(async (event) => {
    await verifyToken(event);

    if (event.node.req.method !== 'POST') {
        throw createError({
            statusCode: 404,
            message: "error.notFound",
        });
    }

    const { id, code, origin } = await readBody(event);

    if (!id || !code || !origin) {
        throw createError({
            statusCode: 400,
            message: "error.badRequest",
        });
    }

    try {
        const auth:AuthInterface = AuthFactory.getAuth();
        const resp = await auth.verify({ id, code, origin });

        if (!resp) {
            throw createError({
                statusCode: 400,
                message: "error.invalidCredentials",
            });
        }

        return resp;
    } catch (err: any) {
        throw createError({
            statusCode: err?.statusCode || 500,
            message: err?.message || "error.internalServerError",
        });
    }
});
