import { verifyToken } from "~~/server/tools/utils";
import { AuthFactory } from "../../../connect/factory/auth.factory";
import { AuthInterface } from "../../../connect/interface/auth.interface";
import { createError } from 'h3';

export default defineEventHandler(async (event) => {
    await verifyToken(event);

    if (event.node.req.method !== 'POST') {
        throw createError({
            statusCode: 404,
            message: "error.notFound",
        });
    }

    const { email, phone } = await readBody(event);

    if (!email && !phone) {
        throw createError({
            statusCode: 400,
            message: "error.notFound",
        });
    }

    try {
        const auth:AuthInterface = AuthFactory.getAuth();
        let resp: any = null;
        if (email) {
            resp = await auth.checkUserByEmail({ email });
        }

        if (phone) {
            resp = await auth.checkUserByPhone({ phone });
        }

        if (!resp) {
            throw createError({
                statusCode: 400,
                message: "error.invalidCredentials",
            });
        }

        return resp;
    } catch (err: any) {
        throw createError({
            statusCode: err?.statusCode || 500,
            message: err?.message || "error.internalServerError",
        });
    }
});
