import { 
    parametersLogin, 
    parametersRegister,
    parametersfindUser<PERSON>yId,
    parametersfind<PERSON>serByEmail,
    parametersfindUserByPhone,
    verifyOrigin,
    ForgotPasswordParameters,
    UpdatePasswordParameters
} from "./database.interface";

export interface AuthInterface {
    login(parameters: parametersLogin): Promise<any>;
    register(parameters: parametersRegister): Promise<any>;
    findUserById(parameters: parametersfindUserById): Promise<any>;
    findUserByEmail(parameters: parametersfindUserByEmail): Promise<any>;
    checkUserByEmail(parameters: parametersfindUserByEmail): Promise<any>;
    checkUserByPhone(parameters: parametersfindUserByPhone): Promise<any>;
    verify(parameters: verifyOrigin): Promise<any>;
    forgotPassword(parameters: ForgotPasswordParameters): Promise<any>;
    updatePassword(parameters: UpdatePasswordParameters): Promise<any>;
    updateProfile(parameters: any): Promise<any>;
    getAddresses(parameters: any): Promise<any>;
    getSales(parameters: any): Promise<any>;
    insertAddress(parameters: any): Promise<any>;
    deleteAddress(parameters: any): Promise<any>;
}
