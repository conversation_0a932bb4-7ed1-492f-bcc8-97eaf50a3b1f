import { generateTokens, getHashedPassword } from "../../../connect/hasura/tools/auth";
import { parametersRegister, registerPayload, verifyOrigin } from "../../interface/database.interface";
import { useGraphQL } from "../graphqlService";
import { add24Hours } from "../../../tools/utils";
import { createError } from 'h3';

export const register = async(parameters: parametersRegister): Promise<any> => {
    const { strategy } = parameters;

    if (strategy === 'local') {
        const payload: registerPayload = parameters.payload;
        const { accessToken } = await generateTokens('system', 'system');
        const graphqlClient = useGraphQL().getClient(accessToken);
        const hash = await getHashedPassword(payload.password);
        payload.password = hash;
        payload.email_verify_at = add24Hours();

        const query = `
            mutation insertUser($data: user_insert_input!) {
                insert_user_one(object: $data) {
                    id
                    email
                    email_code_verify
                }
            }
        `;

        try {
            payload.roles = {data: {role_code: "user"}};
            const variables = { data: payload };
            const response: any = await graphqlClient.request(query, variables);

            if (!response?.insert_user_one) {
                throw createError({
                    statusCode: 404,
                    message: "error.registrationFailed",
                });
            }

            return { user: response.insert_user_one };
        } catch (err: any) {
            console.error("Error registering user:", err);
            if (err.response.errors[0].message == 'Uniqueness violation. duplicate key value violates unique constraint "user_email_key"') {
                throw new Error("error.emailAlreadyRegistered");
            }
            throw err;
        }
    }

    throw new Error("error.methodNotImplemented");
}

export const verify = async(parameters: verifyOrigin): Promise<any> => {
    const { id, code, origin }: any = parameters;
    const { accessToken } = await generateTokens('system', 'system');
    const graphqlClient = useGraphQL().getClient(accessToken);
    let query = '';

    if (origin == 'email') {
        query = `
            mutation VerifyEmail($id: uuid, $code: String) {
                update_user(where: {id: {_eq: $id}, email_code_verify: {_eq: $code}}, _set: {email_verify: true, email_verify_at: "now()", email_code_verify: null}) {
                    affected_rows
                }
            }
        `;
    } else {
        query = `
            mutation VerifyPhone($id: uuid, $code: String) {
                update_user(where: {id: {_eq: $id}, phone_code_verify: {_eq: $code}}, _set: {phone_verify: true, phone_verify_at: "now()", phone_code_verify: null}) {
                    affected_rows
                }
            }
        `;
    }

    try {
        const variables = { id, code };
        const response: any = await graphqlClient.request(query, variables);

        return response.update_user;
    } catch (err: any) {
        console.error("Error verify code:", err);
        throw new Error("error.verifyFailed");
    }
}
