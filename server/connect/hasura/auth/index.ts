import { login } from "./login";
import { register, verify } from "./register";
import { findUserById, findUserByEmail, checkUserByEmail, checkUserByP<PERSON> } from "./findUser";
import { AuthInterface } from "../../interface/auth.interface";
import { 
    parametersLogin, 
    parametersRegister,
    parametersfindUserById,
    parametersfindUserByEmail,
    parametersfindUserByPhone,
    verifyOrigin,
    ForgotPasswordParameters,
    UpdatePasswordParameters
} from "../../interface/database.interface";
import { forgotPassword, updatePassword } from "./password";
import { update as updateProfile, getAddresses, getSales, insertAddress, deleteAddress } from "./profile";

export class <PERSON><PERSON><PERSON><PERSON> implements AuthInterface {
    // #### Login ####
    async login(parameters: parametersLogin): Promise<any> {
        return login(parameters);
    }

    // #### Register ####
    async register(parameters: parametersRegister): Promise<any> {
        return register(parameters);
    }

    // #### FindUserById ####
    findUserById(parameters: parametersfindUserById): Promise<any> {
        return findUserById(parameters);
    }

    // #### FindUserByEmail ####
    async findUserByEmail(parameters: parametersfindUserByEmail): Promise<any> {
        return findUserByEmail(parameters);
    }

    checkUserByEmail(parameters: parametersfindUserByEmail): Promise<any> {
        return checkUserByEmail(parameters);
    }

    checkUserByPhone(parameters: parametersfindUserByPhone): Promise<any> {
        return checkUserByPhone(parameters);
    }

    verify(parameters: verifyOrigin): Promise<any> {
        return verify(parameters);
    }

    forgotPassword(parameters: ForgotPasswordParameters): Promise<any> {
        return forgotPassword(parameters);
    }

    updatePassword(parameters: UpdatePasswordParameters): Promise<any> {
        return updatePassword(parameters);
    }

    updateProfile(parameters: UpdatePasswordParameters): Promise<any> {
        return updateProfile(parameters);
    }

    getAddresses(parameters: any): Promise<any> {
        return getAddresses(parameters);
    }

    getSales(parameters: any): Promise<any> {
        return getSales(parameters);
    }

    insertAddress(parameters: any): Promise<any> {
        return insertAddress(parameters);
    }

    deleteAddress(parameters: any): Promise<any> {
        return deleteAddress(parameters);
    }
}
