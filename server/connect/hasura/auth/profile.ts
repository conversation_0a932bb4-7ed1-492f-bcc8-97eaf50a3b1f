import { useGraphQL, GraphQLServiceError } from "../graphqlService";
import { generateTokens } from "../tools/auth";

export const update = async(parameters: any): Promise<any> => {
    const { accessToken } = await generateTokens(parameters?.user?.id, parameters?.user?.roles[0].role_code);
    try {
        const response = await useGraphQL().update('user', parameters.payload.data, { id: { _eq: parameters.payload.id } }, `
            returning {
                id
                email
                first_name
                phone
                roles {
                    role_code
                }    
            }
        `, accessToken);

        return response?.update_user?.returning?.[0] || {};
    } catch (err: any) {
        console.error(`Error updating user ${parameters.payload.id}:`, err);
        if (err instanceof GraphQLServiceError) {
            throw err;
        }
        throw new GraphQLServiceError('error.unexpectedError');
    }
}

export const getAddresses = async (parameters: any): Promise<any> => {
    const { accessToken } = await generateTokens(parameters?.user?.id, parameters?.user?.roles[0].role_code);
    try {
        return await useGraphQL().get('user', {
            select: `
                id
                email
                addresses_aggregate {
                    aggregate {
                        count
                    }
                    nodes {
                        id
                        province
                        city
                        address_1
                        address_2
                        zip_code
                        created_at
                    }
                }
            `,
            where: { id: { _eq: parameters.payload.id } }
        }, accessToken);
    } catch (err: any) {
        console.error(`Error getting addresses user ${parameters.payload.id}:`, err);
        if (err instanceof GraphQLServiceError) {
            throw err;
        }
        throw new GraphQLServiceError('error.unexpectedError');
    }
}

export const getSales = async (parameters: any): Promise<any> => {
    const { accessToken } = await generateTokens(parameters?.user?.id, parameters?.user?.roles[0].role_code);
    try {
        return await useGraphQL().get('user', {
            select: `
                id
                email
                sales_aggregate(where: {origin: {_eq: "roly"}}, order_by: {created_at: desc}) {
                    aggregate {
                        count
                    }
                    nodes {
                        id
                        cart_id
                        quantity_items
                        amount
                        discount
                        shipment
                        shipment_type
                        tax
                        total
                        payload
                        created_at
                        status
                        expedition
                        expedition_url
                        address {
                            id
                            address_1
                            address_2
                            zip_code
                            province
                            city
                        }
                        payment {
                            type
                            invoice_id
                        }
                    }
                }
            `,
            where: { id: { _eq: parameters.payload.id } }
        }, accessToken);
    } catch (err: any) {
        console.error(`Error getting sales user ${parameters.payload.id}:`, err);
        if (err instanceof GraphQLServiceError) {
            throw err;
        }
        throw new GraphQLServiceError('error.unexpectedError');
    }
}

export const insertAddress = async (parameters: any): Promise<any> => {
    const { accessToken } = await generateTokens(parameters?.user?.id, parameters?.user?.roles[0].role_code);
    try {
        return await useGraphQL().insert('user_addresses', parameters.payload.data, null, `
            returning {
                id
                province
                city
                address_1
                address_2
                zip_code
                created_at
            }
        `, accessToken);
    } catch (err: any) {
        console.error(`Error inserting address user ${parameters.payload.id}:`, err);
        if (err instanceof GraphQLServiceError) {
            throw err;
        }
        throw new GraphQLServiceError('error.unexpectedError');
    }
}

export const deleteAddress = async (parameters: any): Promise<any> => {
    const { accessToken } = await generateTokens(parameters?.user?.id, parameters?.user?.roles[0].role_code);
    try {
        const response = await useGraphQL().delete('user_addresses', { id: { _eq: parameters.payload.id } }, 'affected_rows', accessToken);
        return response;
    } catch (err: any) {
        console.error(`Error deleting address user ${parameters.payload.id}:`, err);
        if (err instanceof GraphQLServiceError) {
            throw err;
        }
        throw new GraphQLServiceError('error.unexpectedError');
    }
}
