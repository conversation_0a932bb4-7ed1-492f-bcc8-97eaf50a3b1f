import { ForgotPasswordParameters, UpdatePasswordParameters } from "../../interface/database.interface";
import { useGraphQL } from "../graphqlService";
import { generateTokens, getHashedPassword } from '../tools/auth';
import { add24Hours } from "../../../tools/utils";

export const forgotPassword = async (parameters: ForgotPasswordParameters) => {
    const { email, password_code_recovery } = parameters;
    if (!email || !password_code_recovery) throw new Error("error.badRequest");
    
    const { accessToken } = await generateTokens('system', 'system');
    const graphqlClient = useGraphQL().getClient(accessToken);
    const password_recovery_at = add24Hours();

    const query = `
        mutation updatePasswordCodeRecovery($email: String, $password_code_recovery: String, $password_recovery_at: timestamptz) {
            update_user(where: {email: {_eq: $email}}, _set: {password_code_recovery: $password_code_recovery, password_recovery_at: $password_recovery_at}) {
                affected_rows
            }
        }
    `

    try {
        const variables = { email, password_code_recovery, password_recovery_at };
        const response: any = await graphqlClient.request(query, variables);

        return response?.update_user;
    } catch (err: any) {
        console.error("Error verify code:", err);
        throw new Error("error.verifyFailed");
    }
}

export const updatePassword = async (parameters: UpdatePasswordParameters) => {
    const { email, password, code } = parameters;
    if (!email || !password || !code) throw new Error("error.badRequest");
    
    const { accessToken } = await generateTokens('system', 'system');
    const graphqlClient = useGraphQL().getClient(accessToken);
    const hash = await getHashedPassword(password);
    const newPassword = hash;

    const query = `
        mutation updatePassword($email: String, $newPassword: String, $code: String) {
            update_user(where: {email: {_eq: $email}, password_code_recovery: {_eq: $code}}, _set: {password: $newPassword}) {
                affected_rows
            }
        }
    `

    try {
        const variables = { email, newPassword, code };
        const response: any = await graphqlClient.request(query, variables);

        return response?.update_user;
    } catch (err: any) {
        console.error("Error updating password:", err);
        throw new Error("error.passwordUpdateFailed");
    }
}
