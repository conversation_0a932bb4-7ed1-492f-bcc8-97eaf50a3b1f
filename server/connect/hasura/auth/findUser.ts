import { useGraphQL, GraphQLServiceError } from "../graphqlService";
import { parametersfindUserById, parametersfindUserByEmail, parametersfindUserByPhone } from "../../interface/database.interface";
import { generateTokens } from "../tools/auth";
import { createError } from 'h3';

export const findUserById = async (parameters: parametersfindUserById): Promise<any> => {
    const { accessToken } = await generateTokens('system', 'system');
    try {
        const response = await useGraphQL().get('user', {
            select: `
                id
                username
                email
                first_name
                last_name
                phone
                email_verify
            `,
            where: { id: { _eq: parameters.userId } }
        }, accessToken);

        if (response.user.length === 0) {
            throw createError({
                statusCode: 404,
                message: "error.userNotFound",
            });
        }

        return { user: response.user[0] };
    } catch (err: any) {
        console.error("Error fetching user by id:", err);
        if (err instanceof GraphQLServiceError) {
            throw err;
        }
        throw new GraphQLServiceError('error.unexpectedError');
    }
}

export const findUserByEmail = async (parameters: parametersfindUserByEmail): Promise<any> => {
    const { accessToken } = await generateTokens('system', 'system');
    try {
        const response = await useGraphQL().get('user', {
            select: `
                id
                username
                email
                password
                first_name
                last_name
                phone
                email_verify
                email_code_verify
                discount
                roles {
                    role_code
                }
            `,
            where: { email: { _eq: parameters.email } }
        }, accessToken);

        if (response.user.length === 0) {
            throw createError({
                statusCode: 404,
                message: "error.userNotFound",
            });
        }

        return { user: response.user[0] };
    } catch (err: any) {
        console.error("Error fetching user by email:", err);
        if (err instanceof GraphQLServiceError) {
            throw err;
        }
        throw new GraphQLServiceError('error.unexpectedError');
    }
}

export const checkUserByEmail = async (parameters: parametersfindUserByEmail): Promise<any> => {
    const { accessToken } = await generateTokens('system', 'system');
    try {
        const response = await useGraphQL().get('user', {
            select: `
                id
                email_verify
            `,
            where: { email: { _eq: parameters.email } }
        }, accessToken);

        if (response.user.length === 0) {
            throw createError({
                statusCode: 404,
                message: "error.userNotFound",
            });
        }

        return { id: response.user[0].id, verify: response.user[0].email_verify };
    } catch (err: any) {
        console.error("Error fetching user by email:", err);
        if (err instanceof GraphQLServiceError) {
            throw err;
        }
        throw new GraphQLServiceError('error.unexpectedError');
    }
}

export const checkUserByPhone = async (parameters: parametersfindUserByPhone): Promise<any> => {
    const { accessToken } = await generateTokens('system', 'system');
    try {
        const response = await useGraphQL().get('user', {
            select: `
                id
                phone_verify
            `,
            where: { phone: { _eq: parameters.phone } }
        }, accessToken);

        if (response.user.length === 0) {
            throw createError({
                statusCode: 404,
                message: "error.userNotFound",
            });
        }

        return { id: response.user[0].id, verify: response.user[0].phone_verify };
    } catch (err: any) {
        console.error("Error fetching user by phone:", err);
        if (err instanceof GraphQLServiceError) {
            throw err;
        }
        throw new GraphQLServiceError('error.unexpectedError');
    }
}
