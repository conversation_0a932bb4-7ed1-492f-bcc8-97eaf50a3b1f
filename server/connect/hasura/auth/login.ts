import { findUserByEmail } from "./findUser";
import { parametersLogin } from "../../interface/database.interface";
import { comparePassword } from "../tools/auth";
import { createError } from 'h3';

export const login = async (parameters: parametersLogin): Promise<any> => {
    const { strategy } = parameters;

    if (strategy === 'local') {
        const { email, password } = parameters;
        try {
            const { user } = await findUserByEmail({ email: email || '' });

            if (!user || !user?.id) {
                throw createError({
                    statusCode: 400,
                    message: "error.invalidCredentials",
                });
            }

            const isPasswordCorrect = await comparePassword(password || '', user.password);
            if (!isPasswordCorrect) {
                throw createError({
                    statusCode: 400,
                    message: "error.invalidCredentials",
                });
            }

            delete user.password;
            return { user };
        } catch (err: any) {
            console.error("Error login user:", err.message);
            if (err.message == 'error.userNotFound') {
                throw new Error("error.invalidCredentials");
            }
            throw err;
        }
    } else {
        throw new Error("error.strategyNotImplemented");
    }
}
