import fs from 'fs/promises';
import { generateKeyPair, exportJWK } from 'jose';

const PUBLIC_KEY_PATH = 'server/keys/public.jwk.json';
const PRIVATE_KEY_PATH = 'server/keys/private.jwk.json';

const generateAndStoreKeys = async () => {
    const { publicKey, privateKey } = await generateKeyPair('RS256', { extractable: true });

    const publicJWK = await exportJWK(publicKey);
    const privateJWK = await exportJWK(privateKey);

    await fs.writeFile(PUBLIC_KEY_PATH, JSON.stringify(publicJWK, null, 2));
    await fs.writeFile(PRIVATE_KEY_PATH, JSON.stringify(privateJWK, null, 2));

    return { publicJWK, privateJWK };
}

export const loadOrGenerateKeys = async () => {
    try {
        const publicJWK = JSON.parse(await fs.readFile(PUBLIC_KEY_PATH, 'utf8'));
        const privateJWK = JSON.parse(await fs.readFile(PRIVATE_KEY_PATH, 'utf8'));
        return { publicJWK, privateJWK };
    } catch (error) {
        console.info('Keys not found, generating new ones...');
        return generateAndStoreKeys();
    }
}
