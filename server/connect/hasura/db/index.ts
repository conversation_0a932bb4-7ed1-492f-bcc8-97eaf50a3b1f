import { DatabaseInterface } from "../../interface/database.interface";
import { useGraphQL, GraphQLServiceError } from "../graphqlService";
import { generateTokens } from "../tools/auth";
import { getCategory, getColor, getSize, getProducts, getProduct, getPrints, savePurchasePayment } from './store';

export class HasuraDB implements DatabaseInterface {
    async getConfig(): Promise<any> {
        try {
            const { accessToken } = await generateTokens('system', 'system');
            return await useGraphQL().get('roly_config', { select: `
                top_bar_message
                phone_contact
                email_contact
                carousel_category
                social_net
                tax
                notify
                shipping
                shipping_free
                discount
            ` }, accessToken);
        } catch (error) {
            if (error instanceof GraphQLServiceError) {
                throw error;
            }
            throw new GraphQLServiceError('error.unexpectedError');
        }
    }

    async getCategory(): Promise<any> {
        return await getCategory();
    }

    async getColor(): Promise<any> {
        return await getColor();
    }

    async getSize(): Promise<any> {
        return await getSize();
    }

    async getProducts(data: string): Promise<any> {
        return await getProducts(data);
    }

    async getProduct(id: string): Promise<any> {
        return await getProduct(id);
    }

    async getPrints(): Promise<any> {
        return await getPrints();
    }

    async savePurchasePayment(data: any): Promise<any> {
        return await savePurchasePayment(data);
    }
}
