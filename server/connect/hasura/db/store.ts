import { useGraphQL, GraphQLServiceError } from "../graphqlService";
import { generateTokens } from "../tools/auth";
import { useRolyShop } from "~/composables/useRolyShop";

export async function getCategory(): Promise<any> {
    try {
        const { accessToken } = await generateTokens('store', 'store');
        return await useGraphQL().get('roly_category', {
            select: `
                id
                slug
                name
                parent_id
                in_menu
                highlight
                subcategories {
                    id
                    slug
                    name
                    parent_id
                    in_menu
                    highlight
                    subcategories {
                        id
                        slug
                        name
                        parent_id
                        in_menu
                        highlight
                        subcategories {
                            id
                            slug
                            name
                            parent_id
                            in_menu
                            highlight
                        }
                    }
                }
            `,
            where: { parent_id: { _is_null: true } },
            orderBy: { order: "asc" }
        }, accessToken);
    } catch (err: any) {
        console.error('Error getting categories:', err);
        if (err instanceof GraphQLServiceError) {
            throw err;
        }
        throw new GraphQLServiceError('error.unexpectedError');
    }
}

export async function getColor(): Promise<any> {
    try {
        const { accessToken } = await generateTokens('store', 'store');
        return await useGraphQL().get('roly_color', {
            select: `
                id
                name
                hexcode
                comphexcode
                url
            `,
            where: { id: { _neq: '-999' } },
            orderBy: { id: "asc" }
        }, accessToken);
    } catch (err: any) {
        console.error('Error getting colors:', err);
        if (err instanceof GraphQLServiceError) {
            throw err;
        }
        throw new GraphQLServiceError('error.unexpectedError');
    }
}

export async function getSize(): Promise<any> {
    try {
        const { accessToken } = await generateTokens('store', 'store');
        return await useGraphQL().get('roly_size', {
            select: `
                id
                name
            `,
            where: { id: { _neq: '-999' } },
            orderBy: { id: "asc" }
        }, accessToken);
    } catch (err: any) {
        console.error('Error getting sizes:', err);
        if (err instanceof GraphQLServiceError) {
            throw err;
        }
        throw new GraphQLServiceError('error.unexpectedError');
    }
}

export async function getProducts({ filter, limit, offset, order_by }: any): Promise<any> {
    try {
        const { accessToken } = await generateTokens('store', 'store');
        const graphqlClient = useGraphQL().getClient(accessToken);
        const query = `
            query getProducts($filter: roly_product_with_prices_bool_exp, $limit: Int, $offset: Int, $order_by: [roly_product_with_prices_order_by!]) {
                roly_product_with_prices(where: $filter, limit: $limit, offset: $offset, order_by: $order_by) {
                    id
                    modelcode
                    modelname
                    description
                    isoutlet
                    allimages
                    price_box_children
                    price_box_adults
                }
                roly_product_with_prices_aggregate(where: $filter) {
                    aggregate {
                        count
                    }
                }
            }
        `;

        const variables = { filter, limit, offset, order_by };
        const response: any = await graphqlClient.request(query, variables);
        
        if (!response?.roly_product_with_prices) {
            throw new Error("store.error.productsNotFound");
        }

        return response;      
    } catch (err: any) {
        console.error('Error getting products:', err);
        if (err instanceof GraphQLServiceError) {
            throw err;
        }
        throw new GraphQLServiceError('error.unexpectedError');
    }
}

export async function getProduct(id: string): Promise<any> {
    try {
        const { accessToken } = await generateTokens('store', 'store');
        return await useGraphQL().get('roly_product_with_prices', {
            select: `
                id
                active
                modelcode
                modelname
                description
                composition
                observations
                moq
                stepqty
                packunits
                boxunits
                family {
                id
                name
                }
                gender {
                id
                name
                }
                categories {
                    category {
                        name
                        slug
                        parent_id
                    }
                }
                isforchildren
                isnovelty
                isoutlet
                packincr
                unitsincr
                taric
                madein
                allimages
                price_box_children
                price_box_adults
                variants_aggregate(order_by: {size: {id: asc}, color: {id: asc}}) {
                    aggregate {
                        count
                    }
                    nodes {
                        id
                        itemname
                        eancode
                        eanpack
                        eanbox
                        measures
                        size {
                            id
                            name
                        }
                        color {
                            id
                            name
                            hexcode
                            comphexcode
                            url
                        }
                        productimage
                        boxsize
                        weight
                        weightunit
                        type
                        packincr
                        unitsincr
                        price_box
                        price_pack
                        price_unit
                        price_box_conf
                        price_pack_conf
                        price_unit_conf
                        price_box_pvp
                        price_pack_pvp
                        price_unit_pvp
                        viewsimages
                        onhand
                        incoming
                        state
                        canteco
                        isforchildren
                    }
                }
            `,
            where: { id: { _eq: id } }
        }, accessToken);
    } catch (err: any) {
        console.error('Error getting product:', err);
        if (err instanceof GraphQLServiceError) {
            throw err;
        }
        throw new GraphQLServiceError('error.unexpectedError');
    }
}

export async function getPrints(): Promise<any> {
    try {
        const { accessToken } = await generateTokens('store', 'store');
        return await useGraphQL().get('roly_print', {
            select: `
                id
                name
                cliche_rep
                unit_min_rep
                prices(order_by: {name: asc}) {
                    id
                    type
                    name
                    cliche
                    unit_min
                    price_1
                    limit_1
                    price_2
                    limit_2
                    price_3
                    limit_3
                    price_4
                    limit_4
                    price_5
                    limit_5
                    price_6
                    limit_6
                    image
                    type
                    description
                }
            `,
            orderBy: { name: "desc" }
        }, accessToken);
    } catch (err: any) {
        console.error('Error getting prints:', err);
        if (err instanceof GraphQLServiceError) {
            throw err;
        }
        throw new GraphQLServiceError('error.unexpectedError');
    }
}

// const getMarkingPrices = (cartItems: any[]) => {
//     const calculateMarkingPrices = (items: any[]): any => {
//         const typeMap = new Map();
//         const repetitions: any = {};
//         const cliche: any = {};

//         for (const item of items) {
//             const itemQuantity = item.quantity;
//             repetitions[item.slug] = {
//                 repetition: false,
//                 price: 0
//             };

//             let withCliche = [];
//             for (const marking of item.markings) {
//                 const type = marking.price.type;
//                 const priceObj = marking.price;
//                 if (!typeMap.has(type)) {
//                     typeMap.set(type, { total: 0, priceObj: priceObj });
//                 }
//                 const entry = typeMap.get(type);
//                 entry.total += itemQuantity;

//                 if (repetitions[item.slug].repetition === false && marking.clicheRep === true && marking.print.cliche_rep > 0 && (item.quantity <= marking.print.unit_min_rep || marking.print.unit_min_rep === 0)) {
//                     repetitions[item.slug] = {
//                         repetition: true,
//                         price: marking.print.cliche_rep
//                     };
//                 }

//                 if (marking.price.cliche > 0 && !marking.clicheRep) {
//                     withCliche.push({ [marking.price.id]: marking.price.cliche });
//                 }
//             }

//             if (withCliche.length > 0) {
//                 cliche[item.slug] = withCliche.reduce((acc, obj) => ({ ...acc, ...obj }), {});
//             }
//         }

//         const result: any = {};
//         for (const [type, { total, priceObj }] of typeMap) {
//             let selectedPrice = 0;
//             for (let i = 1; i <= 6; i++) {
//                 const limit = priceObj[`limit_${i}`];
//                 const price = priceObj[`price_${i}`];
//                 if (limit === undefined || limit <= 0) continue;
//                 if (total <= limit) {
//                     selectedPrice = price;
//                     break;
//                 }
//             }

//             result[type] = {
//                 quantity: total,
//                 price: selectedPrice
//             };
//         }

//         return {
//             markingPrices: result,
//             repetitions,
//             cliche
//         };
//     }

//     return calculateMarkingPrices(cartItems);
// }

const getDetails = (payload: any) => {
    const details: any[] = [];
    const prices = useRolyShop().calculateMarkingPrices(payload);

    payload.forEach((line: any) => {
        Object.keys(line.variants).forEach((key: any) => {
            details.push({
                description: `${ line.variants[key].variant.itemname } (${ line.variants[key].variant.id })`,
                quantity: line.variants[key].quantity,
                price: line.variants[key].variant.price_box_pvp
            });
        });

        line.markings.forEach((mark: any) => {
            details.push({
                description: `${ mark.print.name } | (${ mark.price.id }) ${ mark.marking.description } ${ (mark.clicheRep) ? '- REPETICIÓN' : '' }`,
                quantity: line.quantity,
                price: prices.markingPrices[mark.price.type].price / 100
            });

            if (mark.price.cliche > 0 && !mark.clicheRep) {
                details.push({
                    description: `Picaje - ${ mark.marking.description }`,
                    quantity: 1,
                    price: prices.cliche[line.slug][mark.price.id] / 100
                });
            }
        });

        if (prices.repetitions[line.slug].repetition) {
            details.push({
                description: `Puesta en máquina - ${ line.product }`,
                quantity: 1,
                price: prices.repetitions[line.slug].price / 100
            });
        }
    });

    return details;
}

export const savePurchasePayment = async (data: any) => {
    const { payload, session } = data;
    const user = session.user.id;
    const role = session.user.roles[0].role_code;
    const { accessToken } = await generateTokens(user, role);
    const graphqlClient = useGraphQL().getClient(accessToken);

    const query = `
        mutation savePurchasePayment($data: [sales_insert_input!]!) {
            insert_sales(objects: $data) {
                affected_rows
            }
        }
    `;

    try {
        const variables = {
            data: {
                cart_id: payload.cart.cartId,
                quantity_items: payload.cart.quantityTotal,
                total: payload.cart.summary.total,
                amount: payload.cart.summary.amount,
                shipment: payload.cart.summary.shipment,
                shipment_type: payload.cart.summary.shipmentType,
                discount: payload.cart.summary.discount,
                tax: payload.cart.summary.tax,
                payload: payload.cart,
                address_id: payload.cart.address_id,
                details: { data: getDetails(payload.cart.lines) },
                status: payload.status,
                origin: 'roly',
                payment: {
                    data: {
                        type: payload.paypal?.type || 'PayPal',
                        payer_id: payload.paypal.payer.payer_id,
                        payer_country: payload.paypal.payer.address.country_code,
                        payer_first_name: payload.paypal.payer.name.given_name,
                        payer_last_name: payload.paypal.payer.name.surname,
                        payer_email: payload.paypal.payer.email_address,
                        invoice_id: payload.paypal.id,
                        total_amount: Number(payload.paypal.purchase_units[0].payments.captures[0].amount.value),
                        net_amount: Number(payload.paypal.purchase_units[0].payments.captures[0].seller_receivable_breakdown.net_amount.value),
                        paypal_fee: Number(payload.paypal.purchase_units[0].payments.captures[0].seller_receivable_breakdown.paypal_fee.value),
                        currency_code: payload.paypal.purchase_units[0].payments.captures[0].amount.currency_code,
                        paypal_created_at: payload.paypal.purchase_units[0].payments.captures[0].create_time,
                        payload: payload.paypal
                    }
                }
            }
        };

        const response = await graphqlClient.request(query, variables) as any[];
        return response;
    } catch (err: any) {
        console.error("Error saving purchase payment:", err?.response?.errors || err.message || err);
        throw err?.response?.errors || err.message || err;
    }
}
