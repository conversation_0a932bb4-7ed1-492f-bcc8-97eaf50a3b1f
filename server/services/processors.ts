import { DatabaseFactory } from "../connect/factory/database.factory";
import { DatabaseInterface } from "../connect/interface/database.interface";

const paymentPaypal = async (data: any, session: any) => {
    const db:DatabaseInterface = DatabaseFactory.getDatabase();
    await db.savePurchasePayment({ payload: data, session });
}

export const excecute = async (processor: string, data: any, session: any) => {
    switch (processor) {
        case 'payment-paypal':
            await paymentPaypal(data, session);
        break;
    }
}
