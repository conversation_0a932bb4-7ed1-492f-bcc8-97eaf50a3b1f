import { H3Event } from "h3";

export const generateRandomNumericCode = (digits: number): string => {
    if (digits <= 0) {
        throw new Error("The number of digits must be greater than 0.");
    }

    const min = Math.pow(10, digits - 1);
    const max = Math.pow(10, digits) - 1;

    const randomCode = Math.floor(Math.random() * (max - min + 1)) + min;
    return randomCode.toString();
}

export const add24Hours = (): Date => {
    const today = new Date();
    today.setHours(today.getHours() + 24);
    return today;
}

export const formatPhoneNumber = (phone: string): string | null => {
    let cleanedPhone = phone.replace(/\s+/g, '');
      
    if (cleanedPhone.startsWith('+34')) {
        cleanedPhone = cleanedPhone.slice(1);
    } else if (!cleanedPhone.startsWith('34')) {
        cleanedPhone = '34' + cleanedPhone;
    }

    return cleanedPhone.length === 11 ? cleanedPhone : null;
}

export const verifyToken = async (event: H3Event) => {
    if (process.env.NODE_ENV === 'development') return;

    const origin = getHeader(event, 'origin') || getHeader(event, 'referer');

    if (!origin || (!origin.includes('ebppublicidad.es') && !origin.includes('aljub.net') && !origin.includes('ebppublicidad.eu') && !origin.includes('ebppublicidad.com')) ) {
        throw createError({
            statusCode: 401,
            message: "error.unauthorized",
        });
    }
}

export const generateSlug = (text: string): string => {
    return text
        .toLowerCase()
        .replace("_", "-")
        .normalize("NFD")
        .replace(/[\u0300-\u036f]/g, "")
        .replace(/[^a-z0-9\s-]/g, "")
        .trim()
        .replace(/\s+/g, "-");
}

export const removeAccents = (texto: string) => {
  return texto.normalize("NFD").replace(/[\u0300-\u036f]/g, "")
}
