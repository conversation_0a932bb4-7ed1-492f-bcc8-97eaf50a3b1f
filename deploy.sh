#!/bin/bash
docker buildx build --platform linux/amd64 -f Dockerfile.build -t nuxt3-builder:v1 --load .
docker run --name nuxt3-builder-container nuxt3-builder:v1
docker cp nuxt3-builder-container:/app/.output ./
docker rm nuxt3-builder-container
docker rmi nuxt3-builder:v1

mkdir -p ./.output/server/email
cp -r ./server/email/templates ./.output/server/email/
tar -cvf ./deploy.tar --exclude='*.map' ./captain-definition ./Dockerfile ./.output/*
caprover deploy -t ./deploy.tar
rm ./deploy.tar


# ******* Despliegue Directo a Caprover *******
# npx nuxi build --dotenv .env.production
# tar -cvf ./deploy.tar --exclude='*.map' ./captain-definition ./Dockerfile ./.output/*
# caprover deploy -t ./deploy.tar
# rm ./deploy.tar
