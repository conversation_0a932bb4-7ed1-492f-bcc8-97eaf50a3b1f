{"name": "ebp-publicidad", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev --host", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "overrides": {"vue": "^3.5.13"}, "dependencies": {"@chenfengyuan/vue-qrcode": "^2.0.0", "@dargmuesli/nuxt-cookie-control": "^8.5.3", "@nuxt/icon": "1.10.3", "@nuxt/image": "1.9.0", "@nuxtjs/google-fonts": "^3.2.0", "@nuxtjs/i18n": "9.3.3", "@nuxtjs/seo": "2.2.0", "@nuxtjs/tailwindcss": "6.12.2", "@pinia/nuxt": "^0.10.1", "@primevue/forms": "4.3.1", "@primevue/nuxt-module": "4.3.1", "@primevue/themes": "4.3.1", "bcryptjs": "^3.0.2", "bullmq": "^5.41.7", "graphql": "^16.10.0", "graphql-request": "^7.1.2", "hogan.js": "^3.0.2", "jose": "^6.0.8", "nodemailer": "^6.10.0", "nuxt": "3.15.4", "nuxt-auth-utils": "^0.5.16", "nuxt-gtag": "^3.0.2", "nuxt-nodemailer": "^1.1.2", "pinia": "^3.0.1", "pinia-plugin-persistedstate": "^4.2.0", "primevue": "^4.3.1", "qrcode": "^1.5.4", "vue": "^3.5.13", "vue-easy-lightbox": "^1.19.0", "vue-router": "^4.5.0", "zod": "^3.24.2"}, "devDependencies": {"@iconify-json/carbon": "^1.2.8", "@types/bcryptjs": "^2.4.6", "postcss-import": "^16.1.0", "sass-embedded": "^1.85.1"}}