import { defineStore } from 'pinia';
import type { Discount } from '~/utils/interfaces';

export interface SocialNet {
    key: string;
    icon: string;
    link: string;
}

export const useInitialData = defineStore('InitialData', {
    state: () => ({
        config: {
            topBarMessage: '',
            socialNet: [] as SocialNet[],
            phoneContact: '',
            emailContact: '',
            carouselCategory: 'Novedades',
            tax: 0,
            notify: [] as string[],
            shipping: 0,
            shippingFree: 0,
            discount: null as Discount | null
        }
    }),

    // Getters
    getters: {
        getConfig (state) {
            return state.config;
        }
    },

    // Actions
    actions: {
        addTopBarMessage(topBarMessage: any) {
            this.config.topBarMessage = topBarMessage;
        },
        pushSocialNet(socialNet: SocialNet) {
            this.config.socialNet.push(socialNet);
        },
        removeSocialNet(key: string) {
            this.config.socialNet = this.config.socialNet.filter(net => net.key !== key);
        },
        addPhoneContact(phoneContact: string) {
            this.config.phoneContact = phoneContact;
        },
        addEmailContact(emailContact: string) {
            this.config.emailContact = emailContact;
        },
        addCarouselCategory(carouselCategory: string) {
            this.config.carouselCategory = carouselCategory;
        },
        addTax(tax: number) {
            this.config.tax = tax / 100;
        },
        addNotify(notify: string[]) {
            this.config.notify = notify;
        },
        addShipping(shipping: number) {
            this.config.shipping = shipping;
        },
        addShippingFree(shippingFree: number) {
            this.config.shippingFree = shippingFree;
        },
        addDiscount(discount: Discount) {
            this.config.discount = discount;
        },
        addAll(config: any) {
            this.config.topBarMessage = config.top_bar_message;
            if (config.social_net && config.social_net?.length > 0) {
                config.social_net.forEach((sn: SocialNet) => {
                    this.config.socialNet.push(sn);
                });
            } else {
                this.config.socialNet = [];
            }
            this.config.phoneContact = config?.phone_contact;
            this.config.emailContact = config?.email_contact;
            this.config.carouselCategory = config?.carousel_category;
            this.config.tax = (config?.tax ?? 0) / 100;
            this.config.notify = config?.notify;
            this.config.shipping = (config?.shipping ?? 0) / 100;
            this.config.shippingFree = (config?.shipping_free ?? 0) / 100;
            this.config.discount = config?.discount || null;
        }
    }
});
