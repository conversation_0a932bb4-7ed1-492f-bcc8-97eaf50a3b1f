<script setup lang="ts">
    const config = useInitialData().getConfig;

    const whatsappUrl = computed(() => {
        return useTools().formatPhoneNumber(config.phoneContact);
    });
</script>

<template>
    <div class="w-fit h-fit fixed bottom-5 left-5 z-[300]">
        <NuxtLink v-if="whatsappUrl" :to="`https://wa.me/${ whatsappUrl }`" target="_blank">
            <Button class="w-[54px] h-[54px] !p-2" severity="success" rounded aria-label="WhatsApp" size="large">
                <Icon name="akar-icons:whatsapp-fill" size="26" class="text-white" />
            </Button>
        </NuxtLink>
    </div>
</template>
