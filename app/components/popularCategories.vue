<script setup lang="ts">
    const categories = [
        { name: "Sport Collection", image: "/images/sport.png", slug: "sport-collection-sports" },
        { name: "WorkWear", image: "/images/workwear.jpg", slug: "workwear-workwear" },
        { name: "<PERSON>bri<PERSON>", image: "/images/abrigos.jpg", slug: "abrigos-coats" },
        { name: "Pantalones", image: "/images/pantalones.jpg" , slug: "pantalones-pant-c" },
        { name: "Sudaderas", image: "/images/sudaderas.png", slug: "sudaderas-sud-cha" },
        { name: "E<PERSON>", image: "/images/eco.jpg", slug: "eco-rolyeco" },
        { name: "Footwear", image: "/images/footwear.jpg", slug: "footwear-footwear" },
    ];
</script>

<template>
    <div class="w-full h-full bg-slate-50">
        <section class="w-full h-full container mx-auto px-2 py-4 md:py-12 flex flex-col justify-center items-center">
            <h2 class="text-2xl md:text-3xl font-semibold">Categorías Destacadas</h2>

            <div class="w-full px-4 py-6 overflow-x-auto md:overflow-visible scrollbar-hide flex justify-start md:justify-center">
                <div class="w-fit flex flex-row gap-10">
                    <div v-for="(category, index) in categories" :key="index" class="flex flex-col items-center">
                        <NuxtLink :to="`/catalogo/${ category.slug }`">
                            <div class="flex items-center justify-center bg-white shadow-lg rounded-full !w-[124px] !h-[124px] md:min-w-0">
                                <NuxtImg :src="category.image" class="!w-28 !h-28 rounded-full object-cover object-top" :alt="category.name" quality="60" format="webp" />
                            </div>
                        </NuxtLink>
                        <span class="mt-2 font-semibold text-gray-800">{{ category.name }}</span>
                    </div>
                </div>
            </div>
        </section>
    </div>
</template>

<style lang="css" scoped>
    .scrollbar-hide {
        -ms-overflow-style: none;  /* IE y Edge */
        scrollbar-width: none;  /* Firefox */
    }
    .scrollbar-hide::-webkit-scrollbar {
        display: none; /* Chrome, Safari y Opera */
    }
</style>
