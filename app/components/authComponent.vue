<script setup lang="ts">
    const { loggedIn, clear, user }: any = useUserSession();
    const menu = ref();
    const items = ref<Object[]>([]);

    const navegate = () => {
        if (loggedIn.value) {
            items.value = [
                {
                    label: user.value?.first_name || '',
                    items: [
                        {
                            label: 'Perfil',
                            icon: 'akar-icons:dashboard',
                            path: '/profile'
                        },
                        {
                            label: 'Salir',
                            icon: 'akar-icons:door'
                        }
                    ]
                }
            ];
        } else {
            items.value = [
                {
                    label: 'Ingresar',
                    path: '/auth'
                },
                {
                    label: 'Registrarse',
                    path: '/auth/register'
                }
            ];
        }
    }

    const toggle = (event: any) => {
        menu.value.toggle(event);
    };

    const exit = async () => {
        await clear();
        navigateTo('/', { redirectCode: 301 });
    }

    watch(loggedIn, (_val) => {
        navegate();
    });

    onMounted(() => {
        navegate();
    });
</script>

<template>
    <div class="flex items-center">
        <Menu ref="menu" id="overlay_menu" :model="items" :popup="true">
            <template #item="{ item, props }">
                <div v-if="item?.path" class="h-full p-2 flex items-center cursor-pointer">
                    <NuxtLink :to="item.path">
                        <div class="flex items-center gap-1">
                            <Icon :name="item.icon || ''" />
                            <span>{{ item.label }}</span>
                        </div>
                    </NuxtLink>
                </div>

                <div v-else class="h-full p-2 flex items-center cursor-pointer" @click="exit">
                    <div class="flex items-center gap-1">
                        <Icon :name="item.icon || ''" />
                        <span>{{ item.label }}</span>
                    </div>
                </div>
            </template>
        </Menu>

        <Avatar class="cursor-pointer max-w-[42px] max-h-[42px]" size="large" style="background-color: #C9E252; color: rgb(2 6 23);" shape="circle" @click="toggle">
            <Icon name="akar-icons:person" size="23" class="text-slate-700" />
        </Avatar>
        
        <!-- <div v-else class="flex items-center gap-2">
            <NuxtLink to="/auth" class="">
                <LoginButton />
            </NuxtLink>

            <NuxtLink to="/auth/register">
                <RegisterButton />
            </NuxtLink>
        </div> -->
    </div>
</template>
