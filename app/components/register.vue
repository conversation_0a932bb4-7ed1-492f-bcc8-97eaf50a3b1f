<script setup lang="ts">
    import { navigateTo, useNuxtApp } from 'nuxt/app';
    import { ref } from 'vue';
    import { z } from 'zod'
    import type { FormData, FormConfig } from '~/components/form/FormTypes';
    import { useToast } from "primevue/usetoast";
    import DynamicForm from "../components/form/DynamicForm.vue";

    const props = defineProps({
        extra: {
            type: Boolean,
            default: true
        }
    });

    const loading = ref(false);
    const { t } = useI18n();
    const toast = useToast();

    const formConfig: FormConfig = {
        stepByStep: false,
        submitLabel: 'REGISTRAR',
        loadingLabel: 'Registrando al usuario...',
        labelType: 'Standard',
        fields: [
            {
                name: 'first_name',
                label: 'Nombre Completo',
                component: 'InputText',
                props: {
                    type: 'text',
                    autofocus: true
                },
                validation: z.string().min(3, { message: "El/Los nombre(s) debe(n) tener al menos 3 caracteres" }),
                defaultValue: '',
                required: true
            },
            {
                name: 'email',
                label: 'Correo Electrónico',
                component: 'InputText',
                props: {
                    type: 'email'
                },
                validation: z.string().email({ message: "Ingresa un email válido" }),
                defaultValue: '',
                required: true
            },{
                name: 'phone',
                label: 'Número de teléfono',
                component: 'InputMask',
                props: {
                    mask: '999 99 99 99'
                },
                validation: z.string().min(12, { message: "Ingresa un número de teléfono válido" }).max(12, { message: "Ingresa un número de teléfono válido" }),
                defaultValue: '',
                required: true
            },
            {
                name: 'password',
                label: 'Contraseña',
                component: 'Password',
                props: {
                    promptLabel: "Elije una contraseña",
                    weakLabel: "Insegura",
                    mediumLabel: "Promedio",
                    strongLabel: "Segura",
                    toggleMask: true
                },
                validation: z.string().min(8, { message: "La contraseña debe tener al menos 8 caracteres" }),
                defaultValue: '',
                required: true
            }
        ]
    };

    const handleSubmit = async (form: FormData) => {
        loading.value = true;
        form.first_name = form.first_name.toUpperCase();
        form.email = form.email.toLowerCase();
        const phone = form.phone.replace(/\s+/g, "");

        if (!phone.startsWith('6') && !phone.startsWith('7')) {
            toast.add({ 
                severity: 'warn', 
                summary: 'Dato incorrecto', 
                detail: 'Debes ingresar un número de teléfono válido', 
                life: 5000 });
            loading.value = false;
            return;
        }

        try {
            const { data } = await $fetch('/api/auth/signup', {
                method: 'POST',
                body: {
                    username: form.email,
                    first_name: form.first_name,
                    phone,
                    email: form.email,
                    password: form.password
                },
            });
 
            if (data.status === 'success') {
                navigateTo(`/auth/register/verify?email=${ form.email }`);
                return;
            }

            toast.add({ 
                severity: 'error', 
                summary: 'Ocurrió un error', 
                detail: t('error.unknownError') || 'error.unknownError', 
            life: 5000 });
        } catch (err: any) {
            toast.add({ 
                severity: 'error', 
                summary: 'Ocurrió un error', 
                detail: t(err.data?.message) || 
                    t(err.message) || 
                    t(err.response.errors[0].message) ||
                    t('error.unknownError') || 'error.unknownError',
                life: 5000 });
        } finally {
            loading.value = false;
        }
    }
</script>

<template>
    <div>
        <Toast />
        <div class="w-full md:w-[460px]">
            <DynamicForm
                :formConfig="formConfig"
                v-model:loading="loading"
                @submit="handleSubmit"
            >
                <template #alternativeTitle v-if="extra">
                    <div class="w-full h-full flex flex-col justify-center items-center pb-10 gap-4">
                        <div class="w-full flex flex-col items-center">
                            <h5 class="text-2xl text-center">Crea tu cuenta</h5>
                            <p class="text-sm font-light flex gap-2">
                                ¿Ya tienes una cuenta?
                                <NuxtLink to="/auth">
                                    <div class="!font-semibold p-0 text-sky-600 hover:text-sky-900 underline underline-offset-2 decoration-2 decoration-sky-600">
                                        Inicia Sesión.
                                    </div>
                                </NuxtLink>
                            </p>
                        </div>
                    </div>
                </template>
            </DynamicForm>
        </div>
    </div>
</template>
