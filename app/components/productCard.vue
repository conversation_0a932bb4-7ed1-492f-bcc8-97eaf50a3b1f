<script setup lang="ts">
    const props = defineProps({
        product: {
            type: Object,
            required: true
        }
    });
</script>

<template>
    <NuxtLink :to="`/producto/${ product.id }`" target="_blank">
        <div class="min-w-[305px] w-[305px] max-w-[305px] h-[500px] bg-white hover:cursor-pointer mb-4">
            <div class="w-full h-[380px]">
                <NuxtImg 
                    :src="product.allimages[0].url" 
                    class="w-full h-full object-cover object-center rounded-lg" 
                    quality="60" format="webp" width="305" 
                    preload placeholder="/images/placeholder.webp"
                />
            </div>

            <div class="w-full h-fit flex flex-col px-1 py-1 text-slate-800">
                <div v-tooltip.top="`${product.modelcode} - ${product.modelname}`" class="w-fit font-semibold text-lg uppercase line-clamp-1"><span class="font-light">{{  product.modelcode }}</span> {{ product.modelname }}</div>
                <div class="text-xs text-gray-400 line-clamp-2">{{ product.description }}</div>
                <div class="text-[15px] mt-1 flex flex-col">
                    <div class="text-sm text-slate-700 font-light">Precios desde</div>
                    <div class="flex justify-between items-center">
                        <div class="w-1/2" v-if="product?.price_box_adults">
                            Adulto: <span class="text-lg font-semibold">{{ product?.price_box_adults }} €</span>
                        </div>
                        <div class="w-1/2" v-if="product?.price_box_children">
                            Niño: <span class="text-lg font-semibold">{{ product.price_box_children }} €</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </NuxtLink>
</template>
