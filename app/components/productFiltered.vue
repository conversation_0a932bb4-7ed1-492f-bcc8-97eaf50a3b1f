<script setup lang="ts">
    const props = defineProps({
        filter: {
            type: Object,
            required: true
        },
        page: {
            type: Number,
            required: true
        },
        search: {
            type: String,
            required: false
        }
    });

    interface FilterQuery {
        filter: any,
        limit: Number,
        offset: Number,
        order_by?: Object
    }

    interface Product {
        id: string | number;
        [key: string]: any;
    }

    const clearFilterQuery = () => {
        return {
            filter: {},
            limit: 21,
            offset: 0,
            order_by: { name: 'asc' }
        }
    }

    const { filter, page, search } = toRefs(props);
    const loading = ref(false);
    const products = ref<Product[]>([]);
    const emit = defineEmits([ 'isComplete' ]);

    watchEffect(async () => {
        loading.value = true;
        
        let filterQuery: FilterQuery = clearFilterQuery();
        filterQuery.offset = page.value * parseInt(filterQuery.limit.toString());
        let variants: any = { };

        if (filter.value?.colorSelected?.length > 0) {
            const orArray: any[] = [];
            filter.value?.colorSelected.forEach((elem: any) => {
                orArray.push({
                    id: {
                        _eq: `${ elem }`
                    }
                })
            });
            variants.color = {};
            variants.color._or = orArray;
        }

        if (filter.value?.sizeSelected?.length > 0) {
            const orArray: any[] = [];
            filter.value?.sizeSelected.forEach((elem: any) => {
                orArray.push({
                    id: {
                        _eq: `${ elem }`
                    }
                })
            });
            variants.size = {}
            variants.size._or = orArray;
        }

        let categoryFilter: string[] = [];
        if (filter.value?.categoriesSelected?.length > 0) {
            categoryFilter = [...filter.value.categoriesSelected];
        }
        if (filter.value?.subcategoriesSelected?.length > 0) {
            categoryFilter = [...categoryFilter, ...filter.value.subcategoriesSelected];
        }
        if (categoryFilter.length > 0) {
            filterQuery.filter.categories = {
                category: { slug: { _in: categoryFilter } }
            }
        }

        if (filter.value?.genderSelected?.length > 0) {
            const filterNI = filter.value.genderSelected.includes('NI');
            const filterNNI = filter.value.genderSelected.filter((elem: any) => elem !== 'NI');

            if (filterNI) {
                filterQuery.filter.isforchildren = { _eq: true };
            }

            if (filterNNI.length > 0) {
                filterQuery.filter.gender = {
                    id: {
                        _in: filterNNI
                    }
                };
            }
        }

        if (search?.value) {
            filterQuery.filter._or = [
                { modelcode: {_ilike: `%${ search.value }%`} },
                { modelname: {_ilike: `%${ search.value }%`} }, 
                { description: {_ilike: `%${ search.value }%`} },
                { description_normalized: {_ilike: `%${ search.value }%`} },
                { composition: {_ilike: `%${ search.value }%`} }, 
                { observations: {_ilike: `%${ search.value }%`} }, 
                { family: {name: {_ilike: `%${ search.value }%`}}},
                { categoriesids: {_ilike: `%${ search.value }%`} },
                { categories: {category: {name: {_ilike: `%${ search.value }%`}}} }
            ];
        }

        filterQuery.filter.variants = variants;
        filterQuery.order_by = filter.value?.sort;

        try {
            const response: any = await useRolyShop().getProducts(filterQuery).finally(() => loading.value = false);
            products.value = page.value === 0 ? (response?.products.value || []) : [...products.value, ...(response?.products.value || [])];

            if (products.value.length === response?.total.value.aggregate.count) {
                emit('isComplete', true);
            } else {
                emit('isComplete', false);
            }
        } catch (err) {
            products.value = [];
            emit('isComplete', true);
            console.error('Error fetching products:', err);
        }
    });
</script>

<template>
    <div>
        <div v-if="products.length > 0" class="w-full h-full px-2 flex flex-wrap justify-center gap-3">
            <div  v-for="product in products">
                <ProductCard :product="product" />
            </div>
        </div>

        <div v-else-if="loading" class="w-full h-[380px] px-2 flex flex-wrap gap-2 rounded-md">
            <Skeleton v-for="n in 9" :key="n" height="380px" width="305px"></Skeleton>
        </div>

        <div v-else class="w-full h-[500px] flex justify-center items-center">
            <AxEmpty message="No se encontraron Productos." />
        </div>
    </div>
</template>
