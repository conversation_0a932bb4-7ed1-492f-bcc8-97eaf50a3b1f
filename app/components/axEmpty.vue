<script setup lang="ts">
    const props = defineProps({
        message: {
            type: String,
            required: true,
            default: 'No se encontraron resultados.'
        }
    });
</script>

<template>
    <div class="!w-fit !h-fit flex flex-col gap-2 items-center justify-center border-2 border-dotted border-ebp-600 rounded-md p-3">
        <NuxtImg src="/images/empty.webp" alt="No se encontraron resultados" quality="60" format="webp" width="100" />
        <div class="text-md font-bold text-center text-ebp-700">{{ message }}</div>
    </div>
</template>
