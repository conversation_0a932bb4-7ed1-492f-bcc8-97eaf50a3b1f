<script setup lang="ts">
    const props = defineProps({
        cart: {
            type: Object,
            required: true
        },
        step: {
            type: String,
            required: false,
            default: '1'
        },
        total: {
            type: Number,
            required: false,
            default: 0
        },
        prices: {
            type: Object,
            required: true
        }
    });

    const emit = defineEmits(['removeLine']);

    const hasClicheRep = (marking: any) => {
        return marking.some((item: any) => 
            item.print.cliche_rep > 0 && 
            item.clicheRep === true
        );
    }
</script>

<template>
    <div class="h-full" :class="step === '1' ? 'w-full' : 'w-full h-fit md:w-8/12'">
        <DataView v-if="cart" :value="cart.lines" dataKey="id" class="!p-3 !rounded-lg !border-slate-200">
            <template #list="slotProps">
                <div class="flex flex-col">
                    <div v-for="(item, index) in slotProps.items" :key="index" class="pb-3 flex flex-col gap-3 md:gap-2">
                        <div class="w-full flex flex-col sm:items-center p-2 gap-2" :class="{ 'border-t border-surface-200 dark:border-surface-700 pt-6': index !== 0 }">
                            <div v-if="step === '1'" class="w-full flex justify-end gap-3">
                                <div class="w-fit h-fit cursor-pointer" @click="() => navigateTo(`/producto/${item.slug}?itemId=${item.id}`)">
                                    <Icon name="akar-icons:edit" size="22" class="text-sky-600 hover:text-sky-800" />
                                </div>
                                <div class="w-fit h-fit cursor-pointer" @click="emit('removeLine', item.id)">
                                    <Icon name="akar-icons:trash-can" size="22" class="text-red-600 hover:text-red-800" />
                                </div>
                            </div>
                            <div class="w-full flex flex-col md:flex-row gap-3">
                                <div class="w-full md:w-4/6 text-[16px] font-semibold text-center md:text-left">
                                    <div>{{ item.product }}</div>
                                </div>
                                <div class="w-full md:w-2/6 text-right flex flex-col text-[15px]">
                                    <div class="w-full flex flex-col justify-end">
                                        <div class="font-semibold">{{ item.quantity }} <span class="text-[11px] font-light">Und.</span></div>
                                        <div class="font-semibold"><span class="font-semibold">€</span> {{ (item.subtotal / item.quantity).toFixed(2) }} <span class="text-[11px] font-light">c/u.</span></div>
                                    </div>
                                    <div class="font-semibold"><span class="font-light">Subtotal:</span> € {{ (item.subtotal).toFixed(2) }}</div>
                                </div>
                            </div>
                        </div>

                        <div v-for="variantItem in item.variants" :key="variantItem.id" class="w-full p-0 md:pl-6 flex items-center gap-2 text-[15px] font-semibold">
                            <div><NuxtImg :src="variantItem.variant.productimage" width="80" height="80" format="webp" /></div>
                            <div>{{ variantItem.variant.color.name }}</div>
                            <div>{{ variantItem.variant.size.name }}</div>
                            <span>-</span>
                            <div>{{ variantItem.quantity }} <span class="font-light text-[11px]">Und.</span></div>
                        </div>

                        <div v-if="item?.markings?.length > 0">
                            <div class="text-[16px] font-semibold text-center md:text-left my-5 px-2">Marcaje</div>
                            <div v-for="mark in item.markings" :key="mark">
                                <div class="mb-6 ml-2 md:ml-6">
                                    <div class="w-full grid grid-cols-2 py-1">
                                        <div class="w-full flex flex-col">
                                            <div class="w-full flex gap-1 items-center">
                                                <span class="font-semibold">
                                                    <Icon name="akar-icons:check" size="15" class="text-green-600" /> 
                                                    {{ mark.print.name }}
                                                </span>
                                                <span v-if="mark.clicheRep">-</span>
                                                <span v-if="mark.clicheRep" class="text-xs font-semibold text-orange-600"> REPETICIÓN</span>
                                            </div>
                                                
                                            <div class="w-full flex gap-1 items-center">
                                                <span>({{ mark.price.id }})</span>
                                                <span>{{ mark.marking.description }}</span>
                                            </div>
                                        </div>

                                        <div class="w-full h-full flex justify-end items-end font-semibold text-[14px]">
                                            € {{ (item.quantity * prices.markingPrices[mark.price.type].price / 100).toFixed(2) }}
                                        </div>
                                    </div>

                                    <div v-if="mark.price.cliche > 0 && !mark.clicheRep" class="w-full grid grid-cols-2">
                                        <div class="w-full h-full font-semibold">
                                            <Icon name="akar-icons:check" size  ="15" class="text-green-600" />  Picaje - {{ mark.marking.description }}
                                        </div>

                                        <div class="w-full h-full flex justify-end items-end font-semibold text-[14px]">
                                            € {{ (prices.cliche[item.slug][mark.price.id] / 100).toFixed(2) }}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div v-if="prices.repetitions[item.slug].repetition && hasClicheRep(item.markings)" class="mb-6 ml-2 md:ml-6">
                                <div class="w-full grid grid-cols-2 py-1">
                                    <div class="w-full flex flex-col font-semibold">
                                        Puesta en máquina
                                    </div>

                                    <div class="w-full h-full flex justify-end items-end font-semibold text-[14px]">
                                        € {{ (prices.repetitions[item.slug].price / 100).toFixed(2) }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-if="step === '1'">
                        <Divider />
                        <div class="w-full flex justify-between p-2">
                            <div class="font-semibold text-[16px]">TOTAL</div>
                            <div class="font-semibold text-[16px]">€ {{ total.toFixed(2) }}</div>
                        </div>
                    </div>
                </div>
            </template>

            <template #empty>
                <div class="w-full h-full flex flex-col items-center justify-center py-6">
                    <Icon name="akar-icons:shopping-bag" size="30" />
                    <div class="w-full text-center text-[18px] font-semibold">El carrito está vacío</div>
                </div>
            </template>
        </DataView>
    </div>
</template>
