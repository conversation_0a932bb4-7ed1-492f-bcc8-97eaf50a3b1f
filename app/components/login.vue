<script setup lang="ts">
    import { z } from 'zod'
    import type { FormData, FormConfig } from '~/components/form/FormTypes';
    import { useToast } from "primevue/usetoast";
    import DynamicForm from "../components/form/DynamicForm.vue";

    const props = defineProps({
        extra: {
            type: Boolean,
            default: true
        }
    });

    const loading = ref(false);
    const { fetch } = useUserSession();
    const { t } = useI18n();
    const toast = useToast();

    const formConfig: FormConfig = {
        stepByStep: false,
        submitLabel: 'INGRESAR',
        loadingLabel: 'Iniciando Sesión...',
        labelType: 'Standard',
        fields: [
            {
                name: 'email',
                label: 'Correo Electrónico',
                component: 'InputText',
                props: {
                    type: 'email',
                    autofocus: true
                },
                validation: z.string().email({ message: "Ingresa un email válido" }),
                defaultValue: '',
                required: false
            },
            {
                name: 'password',
                label: 'Contrase<PERSON>',
                component: 'Password',
                props: {
                    feedback: false,
                    toggleMask: true
                },
                validation: z.string().min(8, { message: "La contraseña debe tener al menos 8 caracteres" }),
                defaultValue: ''
            }
        ]
    };

    const handleSubmit = async (form: FormData) => {
        loading.value = true;
        form.email = form.email.toLowerCase();

        try {
            const { data, error }: any = await useFetch('/api/auth/login',{
                method: 'POST',
                body: { email: form.email, password: form.password }
            });
            
            if (data.value?.data && data.value?.data?.status === 'success') {
                await fetch();
                const previousUrl = useCookie('previous_url') || '/';
                navigateTo(previousUrl.value);
                return;
            }

            if (data.value?.data?.reason === 'error.unverifiedEmail') {
                navigateTo(`/auth/register/verify?email=${ form.email }`);
                return;
            }

            toast.add({ 
                severity: 'warn', 
                summary: 'No se pudo iniciar', 
                detail: t(error.value?.data?.message) || t(data.value?.data?.reason) || t(error.value?.message) || t('error.unknownError') || 'error.unknownError',
            life: 5000 });
        } catch (err: any) {
            toast.add({ 
                severity: 'error', 
                summary: 'Ocurrió un error', 
                detail: t(err.response.errors[0].message) || 
                    t(err.message) ||
                    t(err.data?.message) ||
                    t('error.unknownError') || 'error.unknownError', 
                life: 3000 });
        } finally {
            loading.value = false;
        }
    }
</script>

<template>
    <div>
        <Toast />
        <div class="w-full md:w-[460px]">
            <DynamicForm
                :formConfig="formConfig"
                v-model:loading="loading"
                @submit="handleSubmit"
            >
                <template #alternativeTitle v-if="extra">
                    <div class="w-full h-full flex flex-col justify-center items-center pb-10 gap-4">
                        <div class="flex flex-col items-center">
                            <h5 class="text-2xl">Bienvenido de nuevo</h5>
                            <p class="text-sm font-light flex gap-2">
                                ¿No está registrado?
                                <NuxtLink to="/auth/register">
                                    <div class="!font-semibold p-0 text-sky-600 hover:text-sky-900 underline underline-offset-2 decoration-2 decoration-sky-600">
                                        Crea una cuenta.
                                    </div>
                                </NuxtLink>
                            </p>
                        </div>
                    </div>
                </template>

                <template #bottom>
                    <div class="w-full flex justify-end !m-2 pr-3">
                        <NuxtLink to="/auth/forgot-password">
                            <span class="text-slate-900 font-semibold">¿Olvidó su contraseña?</span>
                        </NuxtLink>
                    </div>
                </template>
            </DynamicForm>
        </div>
    </div>
</template>
