<script setup lang="ts">
    import { useToast } from "primevue/usetoast";

    const props = defineProps({
        selectable: {
            type: Boolean,
            default: false
        }
    })

    const rolyShop = useRolyShop();
    const toast = useToast();
    const showModal = ref(false);
    const selected = ref<string | null>(null);
    const { t } = useI18n();
    const emit = defineEmits(['select']);
    const { addresses, loading, refresh } = await rolyShop.getAddresses()

    const onDeleteAddress = async (id: string) => {
        try {
            await $fetch('/api/auth/profile/address', {
                method: 'DELETE',
                body: {
                    id
                }
            });
            refresh();

            if (selected.value === id) {
                selected.value = null;
                emit('select', null);
            }
        } catch (err: any) {
            toast.add({ 
                severity: 'error', 
                summary: 'Ocurrió un error', 
                detail: t(err?.response?._data?.message) || 
                    t(err?.message) ||
                    t(err?.data?.message) ||
                    t('error.unknownError') || 'error.unknownError', 
                life: 3000 });
        }
    }

    const onSelectAddress = (address: any) => {
        selected.value = address.id;
        emit('select', address);
    }
</script>

<template>
    <div>
        <div class="w-full flex flex-wrap gap-4 justify-center md:justify-start">
            <Toast />
            <div @click="showModal = true" class="w-[180px] h-[180px] min-w-[180px] min-h-[180px] max-w-[180px] max-h-[180px] flex justify-center items-center border border-slate-200 rounded-md cursor-pointer">
                <Icon name="akar-icons:plus" class="w-16 h-16 text-gray-400" />
            </div>

            <div 
                :id="'address-' + address.id"
                v-if="!loading" 
                v-for="address in addresses.nodes" :key="address.id"
                v-tooltip.top="`${address.address_1} ${address.address_2}`" 
                class="w-[180px] h-[180px] min-w-[180px] min-h-[180px] max-w-[180px] max-h-[180px] flex flex-col gap-1 border border-slate-200 rounded-md cursor-pointer shadow-md p-3"
                :class="(selectable && selected === address.id) ? 'ring-2 ring-blue-800' : ''"
            >
                <div class="w-full flex justify-end">
                    <div class="w-6 h-6 flex justify-center items-center" @click="onDeleteAddress(address.id)">
                        <Icon name="akar-icons:trash-can" class="w-5 h-5 text-red-600 hover:text-red-800 cursor-pointer" />
                    </div>
                </div>
                <div class="w-full h-full" @click="onSelectAddress(address)">
                    <div class="text-lg font-semibold line-clamp-1">{{ address.province.toUpperCase() }}</div>
                    <div class="text-md font-semibold line-clamp-1">{{ address.city.toUpperCase() }}</div>
                    <div class="w-full text-sm font-light mt-3 line-clamp-3">{{ address.address_1 }}</div>
                </div>
            </div>

            <Skeleton v-if="loading" width="180px" height="180px"></Skeleton>
            <Skeleton v-if="loading" width="180px" height="180px"></Skeleton>
        </div>
        <InsertAddress v-model:open="showModal" @addressInserted="(address: any) => { refresh(); onSelectAddress(address); }" />
    </div>
</template>
