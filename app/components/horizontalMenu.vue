<script setup lang="ts">
    const route = useRoute();
    const menu = ref<HTMLElement | null>(null);
    const isOpen = ref(false);
    const activeKey = ref(route.path);
    const items: any[] = [
        {
            label: "NOVEDADES",
            route: "/catalogo/novedades-novelty-roly",
            color: "text-slate-900 hover:text-ebp-700"
        }, {
            label: "CATÁLOGO",
            route: "/catalogo",
            color: "text-slate-900 hover:text-ebp-700"
        }
    ]

    const closeMenu = () => isOpen.value = false;

    const onClickOutside = (event:any) => {
        const nav:any = document.querySelector('nav');
        if (!nav.contains(event.target)) {
            closeMenu();
        }
    };

    onMounted(() => {
        window.addEventListener('click', onClickOutside);
    });

    onBeforeUnmount(() => {
        window.removeEventListener('click', onClickOutside);
    });

    watch(() => route.path, () => {
        activeKey.value = route.path;
        closeMenu();
    });
</script>

<template>
    <nav ref="menu" class="w-full transition-all duration-300 flex flex-col container mx-auto">
        <div class="bg-white bg-opacity-95 transition-all duration-300 h-[70px] px-6">
            <div class="w-full h-full flex justify-between items-center">
                <!-- Logo -->   
                <div class="min-w-[120px] lg:min-w-[140px]">
                    <NuxtLink to="/" title="En EBP Publicidad ofrecemos servicios de Personalización Textil, Serigrafía, Bordado y Estampado">
                        <NuxtImg src="/images/logo.webp" class="transition-all duration-300 w-[120px] md:w-[140px]" :quality="60" alt="Logo EBP Publicidad" />
                    </NuxtLink>
                </div>

                <!-- Menu on desktop -->
                <div class="w-full h-full hidden md:flex justify-center items-center gap-4 text-[13px] font-medium lg:text-base">
                    <NuxtLink to="/"
                        :class="{
                            'text-ebp-600 font-bold': activeKey === '/',
                            'text-slate-900 hover:text-ebp-700': activeKey !== '/'
                        }"
                    >
                        <span>INICIO</span>
                    </NuxtLink>

                    <div v-for="item in items" :key="item.label" class="w-fit flex gap-4">
                        <div class="text-slate-900">|</div>
                        <NuxtLink :to="item.route"
                            :class="{
                                'text-ebp-600 font-bold': activeKey === item.route,
                                'text-slate-900 hover:text-ebp-700': activeKey !== item.route
                            }"
                        >
                            <span>{{ item.label }}</span>
                        </NuxtLink>
                    </div>

                    <div class="w-fit flex gap-4">
                        <div class="text-slate-900">|</div>
                        <CategoriesMenu class="text-slate-900 hover:text-ebp-700" />
                    </div>
                </div>

                <!-- AuthComponent on desktop -->
                <div class="w-fit h-full hidden md:flex justify-end items-center gap-4 text-base transition-all duration-300">
                    <CategoriesMenu type="icon" />
                    <CardComponent />
                    <AuthComponent />
                </div>

                <!-- Menu button on mobile -->
                <div class="w-full h-full md:hidden flex justify-end items-center gap-5">
                    <CategoriesMenu type="icon" />
                    <CardComponent />
                    <button @click="isOpen = !isOpen" @click.stop type="button" class="w-fit text-slate-900 bg-transparent border-0" aria-label="Abrir menú">
                        <Icon class="w-8 h-8" :class="(!isOpen) ? 'block' : 'hidden'" name="akar-icons:text-align-justified" />
                        <Icon class="w-8 h-8" :class="(isOpen) ? 'block' : 'hidden'" name="akar-icons:cross" />
                    </button>
                </div>
            </div>
        </div>

        <transition name="slide-menu">
            <div v-if="isOpen" class="bg-white/70 backdrop-blur-lg text-slate-900 inset-x-0 top-[70px] z-[500] px-6 py-4 md:hidden absolute w-full h-auto shadow-lg">
                <!-- Menu on mobile -->
                <div class="flex flex-col mb-4">
                    <NuxtLink 
                        to="/" 
                        @click="closeMenu" 
                        class="flex items-center my-2"
                        :class="{
                            'text-ebp-600 font-bold': activeKey === '/',
                            'text-slate-900 hover:text-ebp-700': activeKey !== '/'
                        }"
                    >
                        <span class="text-2xl font-bold ml-4">INICIO</span>
                    </NuxtLink>
                    
                    <div v-for="item in items" :key="item.label" class="w-full flex flex-col gap-1">
                        <Divider />
                        <NuxtLink 
                            :to="item.route" 
                            @click="closeMenu" 
                            class="flex items-center my-2"
                            :class="{
                                'text-ebp-600 font-bold': activeKey === item.route,
                                'text-slate-900 hover:text-ebp-700': activeKey !== item.route
                            }"
                        >
                            <span class="text-2xl font-bold ml-4">{{ item.label }}</span>
                        </NuxtLink>
                    </div>

                    <div class="w-full flex flex-col gap-4">
                        <Divider />
                        <CategoriesMenu class="text-2xl font-bold ml-4" />
                    </div>
                </div>

                <div class="justify-center items-center">
                    <Divider />
                    <div class="flex justify-end items-center gap-2 py-2">
                        <!-- AuthComponent on mobile -->
                        <AuthComponent />
                    </div>
                </div>
            </div>
        </transition>
    </nav>
</template>

<style lang="css" scoped>
    .slide-menu-enter-active,
    .slide-menu-leave-active {
        transition: all 0.3s ease-in-out;
    }

    .slide-menu-enter-from {
        opacity: 0;
        transform: translateX(100%);
    }

    .slide-menu-enter-to {
        opacity: 1;
        transform: translateX(0);
    }

    .slide-menu-leave-from {
        opacity: 1;
        transform: translateX(0);
    }

    .slide-menu-leave-to {
        opacity: 0;
        transform: translateX(100%);
    }
</style>
