<script setup lang="ts">
    import { z } from 'zod';
    import type { FormConfig, FormData } from './form/FormTypes';
    import DynamicForm from "~/components/form/DynamicForm.vue";

    const props = defineProps<{
        open: boolean;
    }>();

    const emit = defineEmits(['update:open', 'addressInserted']);
    const showDialog = ref(props.open);
    const loading = ref(false);
    const rolyShop = useRolyShop();
    const { t } = useI18n();
    const toast = useToast();
    const { user }: any = useUserSession();

    const formConfig: FormConfig = {
        stepByStep: false,
        submitLabel: 'Guardar',
        loadingLabel: 'Guardando la dirección...',
        labelType: 'FloatLabel',
        fields: [
            {
                name: 'address_1',
                label: 'Dirección 1',
                component: 'Textarea',
                props: {
                    rows: '5'
                },
                validation: z.string({ message: 'La dirección es requerida.' }).min(10, { message: 'Debe ser una dirección válida.' }),
                defaultValue: '',
                required: true
            }, {
                name: 'address_2',
                label: 'Dirección 2',
                component: 'Textarea',
                props: {
                    rows: '5'
                },
                validation: z.string(),
                defaultValue: ''
            }, {
                name: 'zip_code',
                label: 'Código Postal',
                component: 'InputText',
                props: {
                    type: 'text',
                },
                validation: z.string(),
                defaultValue: ''
            }, {
                name: 'province',
                label: 'Provincia / Ciudad autónoma',
                component: 'InputText',
                props: {
                    type: 'text',
                },
                validation: z.string().nonempty({ message: 'La Provincia / Ciudad autónoma es requerida.' }),
                defaultValue: '',
                required: true
            }, {
                name: 'city',
                label: 'Ciudad',
                component: 'InputText',
                props: {
                    type: 'text',
                },
                validation: z.string().nonempty({ message: 'La Ciudad es requerida.' }),
                defaultValue: '',
                required: true
            }
        ]
    };

    const handleSubmit = async (form: FormData) => {
        loading.value = true;

        try {
            const address = await rolyShop.insertAddress({
                address_1: form.address_1,
                address_2: form.address_2,
                zip_code: form.zip_code,
                province: form.province.toUpperCase(),
                city: form.city.toUpperCase()
            });

            emit('addressInserted', address);
            emit('update:open', false);
        } catch (err: any) {
            toast.add({ 
                severity: 'error', 
                summary: 'Ocurrió un error', 
                detail: t(err.data?.message) || 
                    t(err.message) || 
                    t(err.response.errors[0].message) ||
                    t('error.unknownError') || 'error.unknownError',
                life: 5000 });
        } finally {
            loading.value = false;
        }
    };

    watch(() => props.open, (newVal) => {
        showDialog.value = newVal;
    });

    watch(showDialog, (newVal) => {
        emit('update:open', newVal);
    });
</script>

<template>
    <div>
        <Toast />
        <Dialog v-model:visible="showDialog" modal header="Dirección de envío" :style="{ maxWidth: '600px' }" :breakpoints="{ '1199px': '75vw', '575px': '95vw' }">
            <div class="w-full">
                <DynamicForm
                    class="!max-w-[600px] !mx-0"
                    :formConfig="formConfig"
                    v-model:loading="loading"
                    @submit="handleSubmit"
                />
            </div>
        </Dialog>
    </div>
</template>
