<script setup lang="ts">
    const props = defineProps({
        category: {
            type: String,
            required: true
        }
    });

    const responsiveOptions = ref([
        {
            breakpoint: '1400px',
            numVisible: 5,
            numScroll: 1
        },
        {
            breakpoint: '1199px',
            numVisible: 4,
            numScroll: 1
        },
        {
            breakpoint: '767px',
            numVisible: 2,
            numScroll: 1
        },
        {
            breakpoint: '575px',
            numVisible: 1,
            numScroll: 1
        }
    ]);
    const loading = ref(true);
    const filterQuery = {
        filter: {
            categories: {
                category: {
                    slug: {
                        _in: [props.category]
                    }
                }
            },
            variants: {}
        },
        limit: 10,
        offset: 0,
        order_by: { modelname: 'asc' }
    }

    const { products } = await useRolyShop().getProducts(filterQuery).finally(() => loading.value = false);
</script>

<template>
    <div v-if="!loading && products && products.length > 0" class="w-full h-full flex flex-col gap-6 py-16 bg-slate-100">
        <div class="w-full h-fit flex flex-col items-center mb-4">
            <h2 class="text-center text-2xl md:text-3xl font-semibold">Productos en Tendencia</h2>
        </div>
        <div v-if="!products || products.length === 0" class="w-full h-60 flex justify-center items-center">
            <AxEmpty message="No se encontraron Productos." />
        </div>

        <div v-else class="w-full h-full">
            <ClientOnly >
                <div id="product-carousel" class="container mx-auto px-3 md:px-0">
                    <Carousel :value="products" :numVisible="4" :numScroll="1" :responsiveOptions="responsiveOptions" circular :autoplayInterval="3000">
                        <template #item="slotProps">
                            <NuxtLink :to="`/producto/${ slotProps.data?.id }`" target="_blank">
                                <div class="!h-full">
                                    <div class="!h-[96%] border border-surface-200 dark:border-surface-700 rounded m-2 p-3 hover:shadow-md bg-white flex flex-col justify-between">
                                        <div class="mb-1">
                                            <div class="relative mx-auto">
                                                <img :src="slotProps.data?.allimages[0].url" :alt="slotProps.data.modelname" class="w-full rounded" />
                                            </div>
                                        </div>
                                        <div class="w-full flex flex-col">
                                            <div v-tooltip.top="`${slotProps.data?.modelcode} - ${slotProps.data?.modelname}`">
                                                <div class="mb-1 text-sm text-slate-900">
                                                    <div class="w-fit font-semibold text-lg uppercase line-clamp-1">
                                                        <span class="font-light">{{ slotProps.data?.modelcode }}</span> {{ slotProps.data?.modelname }}
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="text-xs text-gray-400 line-clamp-2">{{ slotProps.data?.description }}</div>

                                            <div class="flex justify-between items-center mt-2">
                                                <div class="mt-0 font-semibold text-lg">
                                                    <span class="text-xs font-light">Desde</span> 
                                                    {{ (slotProps.data.price_box_children) ? slotProps.data.price_box_children : slotProps.data.price_box_adults }} €
                                                </div>
                                                <Tag v-if="slotProps.data.price_box_children" severity="warn" value="Niños">
                                                    <span class="text-[9px]">Niños</span>
                                                </Tag>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </NuxtLink>
                        </template>
                    </Carousel>
                </div>
            </ClientOnly>
        </div>
    </div>
</template>
