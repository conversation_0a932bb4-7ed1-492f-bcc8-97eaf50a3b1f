export const useRolyShop = () => {
    const systemConfig = useRuntimeConfig();

    const getColors = (lang: Ref<string> | string = 'esp') => {
        const langValue = isRef(lang) ? lang.value : lang;
        
        const { data, error, status, refresh } = useFetch<{ data: { colors: string[] } }>(
            `/api/roly/store/color`, {
                query: { lang: langValue },
                method: 'GET',
            }
        );
    
        const colors = computed(() => data.value?.data?.colors ?? []);
        const loading = computed(() => status.value === 'pending' || status.value === 'idle');
    
        return { 
            colors,
            loadingColors: loading,
            error,
            refresh 
        };
    }

    const getCategories = (highlight: boolean = false) => {
        const { data, status, error, refresh } = useFetch<{ data: { categories: any[] } }>(
            `/api/roly/store/category`, {
                method: 'GET',
            }
        );

        const categories = computed(() =>
            highlight ? data.value?.data?.categories.filter(cat => cat.highlight) : data.value?.data?.categories || []
        );
        const loading = computed(() => status.value === 'pending' || status.value === 'idle');

        return { 
            categories,
            loading: loading,
            error,
            refresh 
        };
    }

    const getSizes = (lang: Ref<string> | string = 'esp') => {
        const langValue = isRef(lang) ? lang.value : lang;
    
        const { data, error, status, refresh } = useFetch<{ data: { sizes: string[] } }>(
            `/api/roly/store/size`, {
                query: { lang: langValue },
                method: 'GET',
                lazy: true,
                server: false
            }
        );
    
        const sizes = computed(() => data.value?.data.sizes || []);
        const loading = computed(() => status.value === 'pending' || status.value === 'idle');
    
        return { 
            sizes,
            loadingSizes: loading,
            error,
            refresh 
        };
    };

    const getProducts = async (query: any) => {
        const products = ref([]);
        const total = ref(0);

        try {
            const data: any = await $fetch(`/api/roly/store/product`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ data: query }),
            });
    
            products.value = data.data.products;
            total.value = data.data.total;
        } catch (error) {
            console.error('Error fetching products:', error);
        }

        return { products, total };
    };

    const getProduct = (slug: string) => {
        const data = ref(null);
        const error = ref(null);
        const loading = ref(true);

        useFetch(`/api/roly/store/product/${slug}`, { method: 'GET' }).then((res: any) => {
            data.value = res.data.value?.data?.product?.[0] ?? null;
        }).catch((err) => {
            error.value = err;
        }).finally(() => {
            loading.value = false;
        });

        return { 
            product: computed(() => data.value),
            loading, 
            error 
        };
    };

    const getPrints = () => {
        const { data, error, status, refresh } = useFetch<any>(
            `/api/roly/store/product/print`, {
                method: 'GET',
            }
        );

        const prints = computed(() => data.value?.data?.prints ?? []);
        const loading = computed(() => status.value === 'pending' || status.value === 'idle');

        return { 
            prints,
            loadingPrints: loading,
            error
        };
    }

    const getAddresses = async () => {
        const { data, error, status, refresh } = useFetch<any>(
            `/api/auth/profile/address`, {
                method: 'GET',
            }
        );

        const addresses = computed(() => data.value?.data?.user?.[0]?.addresses_aggregate ?? []);
        const loading = computed(() => status.value === 'pending' || status.value === 'idle');

        return { 
            addresses,
            loading,
            error,
            refresh 
        };
    }

    const insertAddress = async (body: any) => {
        const { data }: any = await $fetch('/api/auth/profile/address', {
            method: 'POST',
            body: JSON.stringify(body)
        });
        return data;
    }

    const getSales = async () => {
        const { data, error, status, refresh } = useFetch<any>(
            `/api/auth/profile/sales`, {
                method: 'GET',
            }
        );

        const sales = computed(() => data.value?.data?.user?.[0]?.sales_aggregate ?? []);
        const loading = computed(() => status.value === 'pending' || status.value === 'idle');

        return { 
            sales,
            loading,
            error,
            refresh 
        };
    }

    const calculateMarkingPrices = (items: any[]): any => {
        const typeMap = new Map();
        const repetitions: any = {};
        const cliche: any = {};

        for (const item of items) {
            const itemQuantity = item.quantity;
            repetitions[item.slug] = {
                repetition: false,
                price: 0
            };

            let withCliche = [];
            for (const marking of item.markings) {
                const type = marking.price.type;
                const priceObj = marking.price;
                if (!typeMap.has(type)) {
                    typeMap.set(type, { total: 0, priceObj: priceObj });
                }
                const entry = typeMap.get(type);
                entry.total += itemQuantity;

                if (repetitions[item.slug].repetition === false && marking.clicheRep === true && marking.print.cliche_rep > 0 && (item.quantity <= marking.print.unit_min_rep || marking.print.unit_min_rep === 0)) {
                    repetitions[item.slug] = {
                        repetition: true,
                        price: marking.print.cliche_rep
                    };
                }

                if (marking.price.cliche > 0 && !marking.clicheRep) {
                    withCliche.push({ [marking.price.id]: marking.price.cliche });
                }
            }

            if (withCliche.length > 0) {
                cliche[item.slug] = withCliche.reduce((acc, obj) => ({ ...acc, ...obj }), {});
            }
        }

        const result: any = {};
        for (const [type, { total, priceObj }] of typeMap) {
            let selectedPrice = 0;
            for (let i = 1; i <= 6; i++) {
                const limit = priceObj[`limit_${i}`];
                const price = priceObj[`price_${i}`];
                if (limit === undefined || limit <= 0) continue;
                if (total <= limit) {
                    selectedPrice = price;
                    break;
                }
            }

            result[type] = {
                quantity: total,
                price: selectedPrice
            };
        }

        return {
            markingPrices: result,
            repetitions,
            cliche
        };
    }

    const markingTotal = (prices: any) => {
        if (!prices) return 0;
        let markTotal = 0;
        let repetitionTotal = 0;
        let clicheTotal = 0;

        for (const [type, { price, quantity }] of Object.entries(prices.markingPrices as Record<string, { price: number; quantity: number }>)) {
            markTotal += price * quantity;
        }
    
        for (const [slug, { repetition, price }] of Object.entries(prices.repetitions as Record<string, { repetition: boolean; price: number }>)) {
            if (repetition) {
                repetitionTotal += price;
            }
        }

        for (const [id, clicheTypes] of Object.entries(prices.cliche || {})) {
            for (const [type, price] of Object.entries(clicheTypes || {})) {
                clicheTotal += price;
            }
        }
    
        const total = (markTotal + repetitionTotal + clicheTotal) / 100;
        return total;
    };

    return {
        getColors,
        getCategories,
        getSizes,
        getProducts,
        getProduct,
        getPrints,
        getAddresses,
        insertAddress,
        getSales,
        calculateMarkingPrices,
        markingTotal
    }
}
