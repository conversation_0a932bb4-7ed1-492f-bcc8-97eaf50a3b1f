export interface SocialNet {
    key: string;
    icon: string;
    link: string;
}

export interface Config {
    topBarMessage: string;
    socialNet: SocialNet[];
    phoneContact: string;
    emailContact: string;
    carouselCategory: string;
}

const isObject = (variable: Config) => 
    variable !== null && typeof variable === 'object' && !Array.isArray(variable);

export default defineNuxtPlugin(async (nuxtApp) => {
    const config = useInitialData();
    const systemConfig = useRuntimeConfig();

    const siteConfig = useState<Config | null>('siteConfig', () => null);

    if (!siteConfig.value) {
        const { data } = await useAsyncData('siteConfig', () =>
            fetch(`${ systemConfig.public.site.url }/api/roly/system/config`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            }}).then(res => res.json())
        );

        const response: Config = data.value?.data?.config[0] || {};

        if (isObject(response)) {
            config.addAll(response);
            siteConfig.value = response;
        }
    }
});
