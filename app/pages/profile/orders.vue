<script setup lang="ts">
    const rolyShop = useRolyShop();
    const showDetails = ref<boolean>(false);
    const detailsSelected = ref<any>(null);

    const { sales = { nodes: [] }, loading, error, refresh } = await rolyShop.getSales();

    const openDetails = (detail: any) => {
        detailsSelected.value = detail;
        showDetails.value = true;
    }
</script>

<template>
    <div class="w-full min-h-[400px] container mx-auto flex flex-col md:flex-row py-4">
        <Toast />
        <div class="!w-[238px] border-l md:border-r md:border-l-0 ml-6 md:ml-0 pr-6 py-3">
            <ProfileMenu />
        </div>
        <div class="w-full md:!w-[calc(100%-238px)] px-2 md:pl-6 py-6 flex flex-col gap-8">
            <div>
                <div class="text-xl font-light px-2 md:px-0">Historial de pedidos</div>
                <Divider />

                <div v-if="!loading">
                    <DataTable :value="sales?.nodes" tableStyle="min-width: 50rem">
                        <template #empty> Pedidos no encontrados. </template>
                        <Column field="cart_id" header="Referencia">
                            <template #body="slotProps">
                                <Button @click="openDetails(slotProps?.data?.payload ?? {})" variant="link">
                                    <span class="text-ebp-700 hover:text-ebp-900">{{ slotProps?.data?.cart_id }}</span>
                                </Button>
                            </template>
                        </Column>
                        <Column field="created_at" header="Fecha">
                            <template #body="slotProps">
                                {{ new Date(slotProps?.data?.created_at).toLocaleDateString('es-ES', { day: '2-digit', month: '2-digit', year: 'numeric' }) }}
                            </template>
                        </Column>
                        <Column field="quantity_items" header="Items"></Column>
                        <!-- <Column field="subtotal" header="Sub Total">
                            <template #body="slotProps">
                                € {{ (slotProps.data.amount - slotProps.data.discount + slotProps.data.shipment).toFixed(2) }}
                            </template>
                        </Column>
                        <Column field="total" header="IVA">
                            <template #body="slotProps">
                                € {{ slotProps.data.tax }}
                            </template>
                        </Column> -->
                        <Column field="total" header="Total">
                            <template #body="slotProps">
                                € {{ slotProps?.data?.total }}
                            </template>
                        </Column>
                        <Column field="status" header="Estado"></Column>
                        <Column field="pago" header="Pago">
                            <template #body="slotProps">
                                {{ slotProps?.data?.payment?.type || 'N/A' }}
                            </template>
                        </Column>
                        <Column field="comprobante" header="Comprobante">
                            <template #body="slotProps">
                                {{ slotProps?.data?.payment?.invoice_id || 'N/A' }}
                            </template>
                        </Column>
                        <Column field="expedition" header="N. Expedición">
                            <template #body="slotProps">
                                <a 
                                    v-if="slotProps?.data?.expedition_url && slotProps?.data?.expedition" 
                                    :href="slotProps?.data?.expedition_url" 
                                    target="_blank"
                                    class="text-sky-600 hover:text-sky-900"
                                >{{ slotProps?.data?.expedition }}</a>
                                <span v-else-if="slotProps?.data?.expedition">{{ slotProps?.data?.expedition }}</span>
                            </template>
                        </Column>
                    </DataTable>
                </div>

                <div v-else class="w-full">
                    <Skeleton width="100%" height="200px" />
                </div>
            </div>
        </div>

        <Dialog v-model:visible="showDetails" modal header="Detalle de la Orden" :style="{ width: '50vw' }" :breakpoints="{ '1199px': '75vw', '575px': '90vw' }">
            <ProductDetails v-if="detailsSelected?.summary" :cartLines="detailsSelected" />
            <template #footer>
                <Button label="Cerrar" severity="secondary" @click="showDetails = false" autofocus />
            </template>
        </Dialog>
    </div>
</template>
