<script setup lang="ts">
    import { z } from 'zod';
    import type { FormData, FormConfig } from '~/components/form/FormTypes';
    import DynamicForm from "~/components/form/DynamicForm.vue";

    const loading = ref(false);
    const loadingPwd = ref(false);
    const { t } = useI18n();
    const toast = useToast();
    const systemConfig = useInitialData().config;
    const { user: userSession, fetch, clear }: any = useUserSession();
    // const user = ref(userSession);
    const user = toRef(userSession);
    const codeSended = ref(false);
    const sendingCode = ref(false);
    const disabled = ref(false);
    const durationInMinutes = 2;
    const remainingTime = ref(durationInMinutes * 60);

    const formattedTime = computed(() => {
        const minutes = Math.floor(remainingTime.value / 60);
        const seconds = remainingTime.value % 60;
        return `${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
    });

    const formConfig: FormConfig = {
        stepByStep: false,
        submitLabel: 'Enviar',
        loadingLabel: 'Actualizando el perfil...',
        labelType: 'Standard',
        fields: [
            {
                name: 'first_name',
                label: 'Nombre Completo',
                component: 'InputText',
                props: {
                    type: 'text',
                    autofocus: true
                },
                validation: z.string().min(3, { message: "El/Los nombre(s) debe(n) tener al menos 3 caracteres" }),
                defaultValue: user.value?.first_name ?? '',
                required: true
            },
            {
                name: 'email',
                label: 'Correo Electrónico',
                component: 'InputText',
                props: {
                    type: 'email',
                    disabled: true
                },
                defaultValue: user.value?.email ?? '',
                required: false
            },{
                name: 'phone',
                label: 'Número de teléfono',
                component: 'InputMask',
                props: {
                    mask: '999 99 99 99'
                },
                validation: z.string().min(12, { message: "Ingresa un número de teléfono válido" }).max(12, { message: "Ingresa un número de teléfono válido" }),
                defaultValue: user.value?.phone?.toString()?.replace(/(\d{3})(\d{2})(\d{2})(\d{2})/, '$1 $2 $3 $4') ?? '',
                required: true
            }
        ]
    };

    const formConfigPwd: FormConfig = {
        stepByStep: false,
        submitLabel: 'Enviar',
        loadingLabel: 'Enviando...',
        labelType: 'Standard',
        fields: [
            {
                name: 'password',
                label: 'Su Nueva Contraseña',
                component: 'Password',
                props: {
                    promptLabel: "Elije una contraseña",
                    weakLabel: "Insegura",
                    mediumLabel: "Promedio",
                    strongLabel: "Segura",
                    toggleMask: true
                },
                validation: z.string().min(8, { message: "La contraseña debe tener al menos 8 caracteres" }),
                defaultValue: '',
                required: true
            }, {
                name: 'otp',
                component: 'InputOtp',
                props: {
                    autofocus: true,
                    integerOnly: true,
                    length: 6,
                    class: '!max-w-[300px] text-center'
                },
                validation: z.string({ message: "Ingrese el código recibido" }),
                defaultValue: '',
                required: true
            }
        ]
    };

    const handleSubmit = async (form: FormData) => {
        loading.value = true;
        form.first_name = form.first_name?.toUpperCase() || '';
        form.email = form.email?.toLowerCase() || '';
        const phone = form.phone?.replace(/\s+/g, "") || '';

        try {
            const { data }: any = await $fetch('/api/auth/profile', {
                method: 'PUT',
                body: {
                    ...form,
                    phone
                }
            });

            if (data?.status && data?.status === 'success') {
                fetch();
                user.value.first_name = form.first_name;
                toast.add({ 
                    severity: 'success', 
                    summary: 'Datos actualizados', 
                    detail: '',
                    life: 5000 
                });
            } else {
                toast.add({ 
                    severity: 'error', 
                    summary: 'Ocurrió un error', 
                    detail: t('error.unknownError') || 'unknownError',
                    life: 5000 
                });
            }
        } catch (err: any) {
            toast.add({ 
                severity: 'error', 
                summary: 'Ocurrió un error', 
                detail: t(err?.data?.message) || 
                    t(err?.message) || 
                    t(err?.response?.errors[0]?.message) ||
                    t('error.unknownError') || 'error.unknownError',
                life: 5000 });
        } finally {
            loading.value = false;
        }
    }

    const handleSubmitPwd = async (form: FormData) => {
        const email = userSession.value?.email;

        if (!email) {
            await clear();
            return navigateTo('/auth');
        }
    
        if (form.otp && form.otp.length === 6) {
            loadingPwd.value = true;

            try {
                const { data } = await $fetch('/api/auth/password/update', {
                    method: 'POST',
                    body: { email, code: form.otp, password: form.password },
                });

                if (data?.status && data?.status === 'success') {
                    await clear();
                    navigateTo(`/auth`);
                    return;
                }

                toast.add({ 
                    severity: 'error', 
                    summary: 'Ocurrió un error', 
                    detail: t('error.unknownError') || 'error.unknownError', 
                life: 5000 });
            } catch (err: any) {
                toast.add({ 
                    severity: 'error', 
                    summary: 'Ocurrió un error', 
                    detail: t(err?.data?.message) || 
                        t(err.message) || 
                        t(err.response.errors[0].message) ||
                        t('error.unknownError') || 'error.unknownError', 
                    life: 3000 });
            } finally {
                loading.value = false;
            }
        } else {
            toast.add({ 
                severity: 'warn', 
                summary: 'Código incorrecto', 
                detail: 'El código no corersponde.', 
                life: 3000 
            });
        }
    }

    const getCode = async () => {
        const email = userSession.value?.email;

        if (email) {
            sendingCode.value = true;

            try {
                await $fetch('/api/auth/password/forgot',{
                    method: 'POST',
                    body: { email }
                });
                codeSended.value = true;
                disabled.value = true;
                useTools().startTimer(durationInMinutes, () => {
                    remainingTime.value--;
                }, () => {
                    disabled.value = false;
                    remainingTime.value = durationInMinutes * 60;
                });
            } catch (err: any) {
                toast.add({ 
                    severity: 'error', 
                    summary: 'Ocurrió un error', 
                    detail: t(err?.data?.message) || 
                        t(err?.message) || 
                        t(err?.response?.errors[0]?.message) ||
                        t('error.unknownError') || 'error.unknownError', 
                    life: 3000 });
            } finally {
                sendingCode.value = false;
            }
        } else {
            await clear();
            navigateTo('/auth');
        }
    }
</script>

<template>
    <div class="w-full min-h-[400px] container mx-auto flex flex-col md:flex-row py-4">
        <Toast />
        <div class="!w-[238px] border-l md:border-r md:border-l-0 ml-6 md:ml-0 pr-6 py-3">
            <ProfileMenu />
        </div>
        <div class="w-full md:!w-[calc(100%-238px)] px-2 md:pl-6 py-6 flex flex-col gap-8">
            <div>
                <div class="text-xl font-light px-2 md:px-0">Datos del usuario</div>
                <Divider />
                <DynamicForm
                    class="!max-w-[600px] !mx-0"
                    :formConfig="formConfig"
                    v-model:loading="loading"
                    @submit="handleSubmit"
                />
            </div>

            <div>
                <div class="text-xl font-light px-2 md:px-0">Cambio de contraseña</div>
                <Divider />
                <div v-if="codeSended">
                    <DynamicForm
                        class="!max-w-[600px] !mx-0"
                        :formConfig="formConfigPwd"
                        v-model:loading="loadingPwd"
                        @submit="handleSubmitPwd"
                    >
                        <template #alternativeTitle>
                            <div class="w-full h-full flex flex-col justify-center items-center pt-8 pb-10 gap-4">
                                <div class="flex flex-col items-center gap-2">
                                    <h5 class="text-2xl">Actualizar Contraseña</h5>
                                    <p class="text-sm font-light flex gap-2 text-center">
                                        Ingresa tu nueva contraseña y el código recibido en tu correo electrónico
                                    </p>
                                    <div v-if="!disabled" @click="getCode" class="cursor-pointer">
                                        <div class="!font-light p-0 text-ebp-600 hover:text-ebp-900 underline">
                                            ¿No recibió el código?
                                        </div>
                                    </div>
                                    <div v-else>
                                        {{ formattedTime }}
                                    </div>
                                </div>
                            </div>
                        </template>
                    </DynamicForm>
                </div>
                <div v-else class="p-2">
                    <Card class="!max-w-[600px] border border-gray-200 shadow-xl">
                        <template #content>
                            <div class="flex flex-col md:flex-row items-center justify-between gap-4 md:gap-2">
                                <span class="font-medium text-sm text-center">Te enviaremos un código a tu correo electrónico</span>
                                <Button label="Obtener código" size="small" @click="getCode" :loading="sendingCode" />
                            </div>
                        </template>
                    </Card>
                </div>
            </div>

            <div>
                <div class="text-xl font-light px-2 md:px-0">Dirección de envío</div>
                <Divider />
                <div class="p-2">
                    <Addresses />
                </div>
            </div>
        </div>
    </div>
</template>
