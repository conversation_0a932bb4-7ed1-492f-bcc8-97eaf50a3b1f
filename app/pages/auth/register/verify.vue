<script setup lang="ts">
    import { z } from 'zod'
    import type { FormData, FormConfig } from '~/components/form/FormTypes';
    import DynamicForm from "../../../components/form/DynamicForm.vue";
    import { createError } from 'h3';

    const route = useRoute();
    const loading = ref(false);
    const { t } = useI18n();
    const toast = useToast();
    const origin = ref('');
    const email = route.query?.email;
    const phone = route.query?.phone;
    const id = ref(null);

    const formConfig: FormConfig = {
        stepByStep: false,
        submitLabel: 'VERIFICAR',
        loadingLabel: 'Verificando...',
        labelType: 'none',
        fields: [
            {
                name: 'otp',
                component: 'InputOtp',
                props: {
                    autofocus: true,
                    integerOnly: true,
                    length: 6
                },
                validation: z.string({ message: "Ingresa el código recibido" }),
                defaultValue: '',
                required: false
            }
        ]
    };

    const verify = async () => {
        if (email) {
            origin.value = 'email';
        } else if(phone) {
            origin.value = 'phone';
        } else {
            throw createError({
                statusCode: 404,
                message: "error.notFound",
            });
        }

        const body = { [origin.value]: (origin.value === 'email') ? email : phone };

        try {
            const data: any = await $fetch('/api/auth/verify/origin', {
                method: 'POST',
                body: JSON.stringify(body)
            });

            if (data.verify) {
                navigateTo('/');
            }
            
            id.value = data.id;
        } catch (err: any) {
            navigateTo('/');
        }
    }

    const handleSubmit = async (form: FormData) => {
        if (form.otp.length === 6) {
            loading.value = true;

            try {
                const data = await $fetch('/api/auth/verify', {
                    method: 'POST',
                    body: { id: id.value, code: form.otp, origin: origin.value }
                });
    
                if (data.affected_rows === 1) {
                    toast.add({ 
                        severity: 'success', 
                        summary: 'Verificado', 
                        detail: 'Verificado Correctamente.',
                        life: 5000 
                    });
                    await new Promise(resolve => setTimeout(resolve, 1000));
                    if (origin.value === 'email') {
                        navigateTo('/auth');
                    } else {
                        navigateTo('/');
                    }
                } else {
                    toast.add({ 
                        severity: 'warn', 
                        summary: 'Código incorrecto', 
                        detail: 'El código no corersponde.', 
                        life: 3000 
                    });
                }
            } catch (err: any) {
                toast.add({ 
                    severity: 'error', 
                    summary: 'Ocurrió un error', 
                    detail: t(err.data?.message) || 
                        t(err.message) || 
                        t(err.response.errors[0].message) ||
                        t('error.unknownError') || 'error.unknownError', 
                    life: 3000 });
            } finally {
                loading.value = false;
            }
        }
    }

    onMounted(() => {
        verify();
    })
</script>

<template>
    <div class="w-full min-full flex justify-center items-center py-8 md:py-28">
        <Toast />
        <div class="w-[350px]">
            <div class="w-full flex flex-col items-center py-8 gap-2">
                <h5 class="text-center text-xl font-medium">Ingresa el código de verificación</h5>
                <p class="text-center text-sm font-light">Te hemos enviado un código de 6 dígitos a tu {{ origin === 'email' ? 'Correo Electrónico' : 'Número de teléfono' }} para su verificación.</p>
            </div>
            <DynamicForm
                :formConfig="formConfig"
                v-model:loading="loading"
                @submit="handleSubmit"
            />
        </div>
    </div>
</template>
