<script setup lang="ts">
    import { z } from 'zod'
    import type { FormData, FormConfig } from '~/components/form/FormTypes';
    import { useToast } from "primevue/usetoast";
    import DynamicForm from "../../../components/form/DynamicForm.vue";

    const loading = ref(false);
    const { t } = useI18n();
    const toast = useToast();

    const formConfig: FormConfig = {
        stepByStep: false,
        submitLabel: 'ENVIAR',
        loadingLabel: 'Enviando...',
        labelType: 'Standard',
        fields: [
            {
                name: 'email',
                label: 'Correo Electrónico',
                component: 'InputText',
                props: {
                    type: 'email',
                    autofocus: true
                },
                validation: z.string().email({ message: "Ingresa un email válido" }),
                defaultValue: '',
                required: false
            }
        ]
    };

    const handleSubmit = async (form: FormData) => {
        loading.value = true;
        form.email = form.email.toLowerCase();

        try {
            const resp = await useFetch('/api/auth/password/forgot',{
                method: 'POST',
                body: { email: form.email }
            });

            if (resp.status.value === 'error') {
                toast.add({ 
                    severity: 'error', 
                    summary: 'Ocurrió un error',
                    detail: t(resp.error.value?.data?.message) ||
                        t('error.unknownError') || 'error.unknownError',
                life: 3000 });
                return;
            } else navigateTo(`/auth/forgot-password/new-password?email=${ form.email }`);
        } catch (err: any) {
            toast.add({ 
                severity: 'error', 
                summary: 'Ocurrió un error',
                detail: t(err.data?.message) || 
                    t(err.message) || 
                    t(err.response.errors[0].message) ||
                    t('error.unknownError') || 'error.unknownError', 
                life: 3000 });
        } finally {
            loading.value = false;
        }
    }
</script>

<template>
    <div class="w-full min-full flex justify-center items-center py-8 md:py-28">
        <Toast />
        <div class="w-[460px]">
            <DynamicForm
                :formConfig="formConfig"
                v-model:loading="loading"
                @submit="handleSubmit"
            >
                <template #alternativeTitle>
                    <div class="w-full h-full flex flex-col justify-center items-center pb-10 gap-4">
                        <div class="flex flex-col items-center gap-2">
                            <h5 class="text-2xl">Recuperar Contraseña</h5>
                            <p class="text-sm font-light flex gap-2 text-center">
                                Le enviaremos un código para que pueda recuperar su contraseña
                            </p>
                        </div>
                    </div>
                </template>
            </DynamicForm>
        </div>
    </div>
</template>
