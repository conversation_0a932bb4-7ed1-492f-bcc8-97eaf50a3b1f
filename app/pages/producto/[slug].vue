<script setup lang="ts">
    import { type LineCart } from '../../utils/interfaces';

    interface Totals {
        units: number,
        total: number,
        fullTotal: number,
        unitary: number
    }

    const route = useRoute();
    const config = useInitialData().getConfig;
    const slug: string = route.params?.slug as string;
    const toast = useToast();
    const itemId: string = route.query?.itemId as string || '';
    const activeIndex = ref(0);
    const lightboxVisible = ref(false);
    const lightboxIndex = ref(0);
    const loadingProduct = ref(true);
    const colorSelected = ref<any>(null);
    const purchaseQuantity = ref<any>({});
    const itemLine = ref<LineCart | null>(null);
    const variants = ref<any>(null);
    const totals = ref<Totals>({
        units: 0,
        total: 0,
        fullTotal: 0,
        unitary: 0
    });
    const switchMarking = ref(false);
    const switchClicheRep = ref(false);
    const marking = ref<any>([]);
    const printSelected = ref<any>(null);
    const areaSelected = ref<any>(null);
    const imagesForGallery = ref<any[]>([]);
    const cartStore = useCartStore();

    if (!slug) {
        navigateTo('/404');
    }

    const currentCartLines = computed(() => {
        return itemId 
            ? cartStore.cart.lines.filter(line => line.id !== itemId)
            : cartStore.cart.lines;
    });

    const virtualCurrentProduct = computed(() => ({
        markings: marking.value,
        quantity: totals.value.units,
        slug: slug,
        id: itemId || 'temp'
    }));

    const combinedItems = computed(() => {
        return [...currentCartLines.value, virtualCurrentProduct.value];
    });

    const markingPrices = computed(() => 
        useRolyShop().calculateMarkingPrices(combinedItems.value)
    );

    const currentMarkingPrices = computed(() => {
        const localMarkingPrices = markingPrices.value;
        const localCurrentMarkingPrices = useRolyShop().calculateMarkingPrices([virtualCurrentProduct.value]);

        if (localMarkingPrices?.markingPrices && localCurrentMarkingPrices?.markingPrices) {
            for (const [type] of Object.entries(localCurrentMarkingPrices.markingPrices as Record<string, { price: number; quantity: number }>)) {
                if (localMarkingPrices.markingPrices[type]?.price !== undefined) {
                    localCurrentMarkingPrices.markingPrices[type].price = localMarkingPrices.markingPrices[type].price;
                }
            }
        }

        return localCurrentMarkingPrices;
    });

    const currentMarkingTotal = computed(() => 
        useRolyShop().markingTotal(currentMarkingPrices.value)
    );

    const getCurrentPrice = (type: string) => {
        if (!type || !markingPrices.value?.markingPrices) return 0;
        return markingPrices.value.markingPrices[type]?.price || 0;
    }

    const initPurchaseQuantity = (): any => {
        if (itemId) itemLine.value = cartStore.getLineById(itemId);
        if (itemLine.value?.slug && itemLine.value.slug !== slug) navigateTo('/404');

        const itemLineVariants: any = (itemLine.value) ? itemLine.value?.variants : {};
        marking.value = (itemLine.value) ? itemLine.value?.markings : [];
        if (marking.value.length > 0) switchMarking.value = true;
        else switchMarking.value = false;

        variants.value?.forEach((element: { id: string; }) => {
            const id: string = element.id;
            const quantity = itemLineVariants[id]?.quantity || 0;
            purchaseQuantity.value[id] = {
                ref: product.value?.id,
                product: `${ product.value?.modelname } (${ product.value?.id })`.trim(),
                variant: element,
                quantity
            };
        });
    }

    const { product, loading, error }: any = useRolyShop().getProduct(slug);
    const { prints, loadingPrints, errorPrints }: any = useRolyShop().getPrints();

    const openLightbox = () => {
        lightboxIndex.value = activeIndex.value;
        lightboxVisible.value = true;
    };

    const getUniqueColors = (variants: {
        color: { id: string; name: string }, 
        id: string, 
        productimage: string, 
        isforchildren?: boolean, 
        size: { id: string; name: string },
        viewsimages: string;
    }[] = []) => {
        const uniqueColors = new Map();

        variants.forEach(({ color, id, productimage, isforchildren = false, size, viewsimages }) => { 
            if (!uniqueColors.has(color.id)) {
                uniqueColors.set(color.id, { 
                    ...color, 
                    variant_id: id, 
                    productimage,
                    viewsimages: (viewsimages ?? '').split(',').filter(Boolean).map((url: string) => ({ url })),
                    isforchildren: !!isforchildren,
                    '5XL': size.id === '08'
                });
            } else {
                const existingColor = uniqueColors.get(color.id);
                if (isforchildren) {
                    existingColor.isforchildren = true;
                }
                if (size.id === '08') {
                    existingColor['5XL'] = true;
                }
            }
        });

        return Array.from(uniqueColors.values());
    };

    const uniqueColors = computed(() => {
        return getUniqueColors(variants.value || []) ?? [];
    });

    const getVariantsByColor = (colorId: string) => {
        return variants.value?.filter((variant: any) => variant.color.id === colorId) ?? [];
    }

    const getCategoryURL = (category: any) => {
        if (!category?.parent_id) {
            return `/catalogo/${ category?.slug }`;
        } else {
            return `/catalogo?subcategories=${ category?.slug }`;
        }
    }

    const printPrices = computed(() => {
        return prints.value.find((price: any) => price.id === printSelected.value)?.prices;
    });

    const addMarking = (print_id: string, price_id: string) => {
        const print = structuredClone(prints.value.find((print: any) => print.id === print_id));
        const printPrice = structuredClone(print.prices.find((price: any) => price.id === price_id));
        delete print.prices;
        const markingIndex = marking.value.findIndex((obj: any) => obj.print.id === print_id && obj.price.id === price_id);
        const newMarking = {
            print: { ...print },
            price: {  ...printPrice },
            clicheRep: switchClicheRep.value,
            marking: {
                description: `${ printPrice.name }`,
                complement: printPrice.description,
                price: 0
            }
        };

        if (markingIndex !== -1) {
            marking.value[markingIndex] = newMarking;
        } else {
            marking.value.push(newMarking);
        }
    };

    const deleteLineOfMarking = (print_id: string, price_id: string) => {
        marking.value = marking.value.filter((element: any) => !(element.print.id === print_id && element.price.id === price_id));
    }

    const onlyPurchaseQuantity = computed(() => {
        const purchase = Object.fromEntries(
            Object.entries(purchaseQuantity.value).filter(([key, item]: [string, any]) => item.quantity > 0)
        ) as Record<string, any>;

        const units: number = Object.values(purchase).reduce((acc, item) => acc + item.quantity, 0);
        const total: number = Object.values(purchase).reduce((acc, item) => acc + (item.quantity * item.variant.price_box_pvp), 0);
        const fullTotal: number = total + currentMarkingTotal.value;
        const unitary: number = fullTotal / units || 0;

        totals.value.units = units;
        totals.value.total = total;
        totals.value.fullTotal = fullTotal;
        totals.value.unitary = unitary;

        return purchase;
    });

    const deepClone = (obj: any) => {
        return JSON.parse(JSON.stringify(obj));
    }
    const addCart = () => {
        const productItem: LineCart = {
            id: (itemId) ? itemId : useTools().getRandomString(15),
            slug,
            product: `${ product.value.modelname } (${ product.value.id })`.trim(),
            variants: deepClone(onlyPurchaseQuantity.value),
            markings: marking.value,
            quantity: totals.value.units,
            price: 0,
            subtotal: totals.value.total
        }
        useCartStore().addLine(productItem);
        if(itemId) {
            navigateTo(`/cart`);
        } else {
            marking.value = [];
            initPurchaseQuantity();
            toast.add({ 
                severity: 'success', 
                summary: 'Agregado al carrito',
                life: 5000 });
        }
    }

    watch(
        () => ({
            product: product?.value ?? null,
            loading: loading?.value ?? true,
            error: error?.value ?? null
        }),
        ({ product, loading, error }) => {
            loadingProduct.value = loading;
            if (!loading && (!product || error)) {
                navigateTo('/404');
            }

            if (product) {
                variants.value = product?.variants_aggregate?.nodes;
                colorSelected.value = uniqueColors.value[0];
                initPurchaseQuantity();
            }
        },
        { deep: true }
    );

    watch(colorSelected, (newColor) => {
        if (newColor && product.value) {
            imagesForGallery.value = [
                ...newColor.viewsimages,
                ...product.value.allimages
            ];
        }
    });
</script>

<template>
    <div>
        <Toast />
        <div v-if="loadingProduct === false" class="container w-full h-full mx-auto px-4 py-8 flex flex-col gap-6">
            <!-- ####  Info  #### -->
            <div class="w-full h-full grid md:grid-cols-2 gap-6">
                <!-- ####  Left  #### -->
                <div class="w-full !max-w-[612px] h-full flex flex-col gap-4">
                    <Galleria v-model:activeIndex="activeIndex" :value="imagesForGallery" :responsiveOptions="[
                        {
                            breakpoint: '1300px',
                            numVisible: 4
                        },
                        {
                            breakpoint: '575px',
                            numVisible: 3
                        }
                    ]" :numVisible="4" containerStyle="max-width: 612px;" :circular="true" :showItemNavigators="false" :showItemNavigatorsOnHover="false">
                        <template #item="slotProps">
                            <div class="w-full !max-w-[612px] !aspect-square bg-white overflow-hidden" @click="openLightbox">
                                <NuxtImg 
                                    :src="slotProps.item?.url" 
                                    class="!w-full !h-full object-contain object-top" 
                                    quality="80" format="webp" width="612" 
                                    preload placeholder="/images/placeholder.webp"
                                />
                            </div>
                        </template>
                        <template #thumbnail="slotProps" class="!w-[90px] !h-[90px] !max-w-[90px] !max-h-[90px] !min-w-[90px] !min-h-[90px]">
                            <div class="md:!w-[90px] md:!h-[90px] md:!max-w-[90px] md:!max-h-[90px] md:!min-w-[90px] md:!min-h-[90px] !aspect-square overflow-hidden flex justify-center items-center">
                                <NuxtImg 
                                    :src="slotProps.item?.url" 
                                    class="w-full h-full object-cover object-top mx-1" 
                                    quality="40" format="webp" width="90" height="90"
                                    preload placeholder="/images/empty_img.webp"
                                />
                            </div>
                        </template>
                    </Galleria>

                    <ClientOnly>
                        <vue-easy-lightbox
                            :visible="lightboxVisible"
                            :imgs="imagesForGallery.map((slide: any) => slide.url)"
                            :index="lightboxIndex"
                            @hide="lightboxVisible = false"
                        />
                    </ClientOnly>
                </div>

                <!-- ####  Right  #### -->
                <div class="w-full flex flex-col gap-2">
                    <div>
                        <Message severity="warn" class="rounded-lg">
                            <span class="text-sm font-light">Transporte e impuestos no incluidos. {{ config?.topBarMessage }}.</span>
                        </Message>
                    </div>

                    <!-- ####  Product Data  #### -->
                    <div>
                        <h1 class="text-3xl font-bold uppercase text-slate-800">{{ product?.modelname }} <span class="font-extralight">{{ product?.id }}</span></h1>
                        <div class="bg-ebp-500 w-12 h-1 mt-1 mb-3"></div>
                        <p class="whitespace-pre-line text-sm font-light text-slate-500">{{ useTools().addLineBreaks(product?.description) }}</p>
                    </div>

                    <!-- ####  Categories  #### -->
                    <div id="categories" v-if="product?.categories?.length > 0" class="text-base text-slate-500 flex flex-wrap gap-2">
                        <span class="font-semibold text-slate-600">Categorías Relacionadas:</span>
                        <template v-for="(item, i) in product?.categories" :key="item.category?.id" >
                            <div v-if="item.category !== null">
                                <NuxtLink :to="getCategoryURL(item.category)" 
                                    class="underline decoration-ebp-600/80 underline-offset-4 hover:decoration-2 hover:text-slate-900 text-extralight"
                                >
                                    <span class="uppercase">{{ item.category?.name }}</span>
                                </NuxtLink>
                                <span v-if="i < product?.categories?.length - 1">, </span>
                                <span v-else>.</span>
                            </div>
                        </template>
                    </div>

                    <!-- ####  Colors  #### -->
                    <div id="colors" class="flex flex-wrap gap-6 md:gap-4 py-3 justify-center md:justify-normal">
                        <div v-for="color in uniqueColors" :key="color.id" v-tooltip.top="{ value: `${color.id} - ${color.name}`, showDelay: 600 }" class="w-fit h-fit cursor-pointer" @click="() => colorSelected = color">
                            <div class="relative">
                                <div v-if="color.isforchildren" class="absolute top-[-4px] right-[20px] text-[9px] bg-slate-600 text-white px-1">Kids</div>
                                <div v-if="color['5XL']" class="absolute bottom-[18px] right-[20px] text-[9px] bg-slate-600 text-white px-1">5XL</div>

                                <div class="flex flex-col gap-1 justify-center items-center">
                                    <div class="w-9 h-9 border border-slate-300 rounded-full bg-cover bg-center flex justify-center items-center" :class="{ '!border-slate-800 border-2 p-1': color.id === colorSelected.id }">
                                        <div v-if="color.hexcode" class="w-full h-full rounded-full" :style="{ backgroundColor: color.hexcode }"></div>
                                        <div v-else-if="color.comphexcode" class="w-full h-full rounded-full flex">
                                            <div class="w-1/2 rounded-l-full" :style="{ backgroundColor: color.comphexcode.split('/')[0] }"></div>
                                            <div class="w-1/2 rounded-r-full" :style="{ backgroundColor: color.comphexcode.split('/')[1] }"></div>
                                        </div>
                                        <div v-else-if="color.url" class="w-full h-full rounded-full" :style="{ backgroundImage: `url(${color.url})` }"></div>
                                    </div>
                                    <span class="text-[12px]">{{ color.id }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- ####  Price Table  #### -->
                    <div class="flex flex-col gap-1">
                        <div>
                            <div class="text-xl font-semibold text-slate-800">Precio desde</div>
                            <div class="bg-ebp-500 w-12 h-1 mt-1 mb-3"></div>
                        </div>

                        <div v-if="product?.price_box_children || product?.price_box_adults" class="flex items-center gap-4">
                            <div v-if="product?.price_box_adults" class="w-fit h-fit px-8 py-2 rounded-md flex flex-col items-center text-white font-semibold bg-red-700">
                                <div class="text-xl">{{ product?.price_box_adults }} €</div>
                                <div v-if="product?.price_box_children && product?.price_box_adults" class="text-[12px] font-light">Adulto</div>
                            </div>

                            <div v-if="product?.price_box_children" class="w-fit h-fit px-8 py-2 rounded-md flex flex-col items-center text-white font-semibold bg-red-700">
                                <div class="text-xl">{{ product?.price_box_children }} €</div>
                                <div v-if="product?.price_box_children && product?.price_box_adults" class="text-[12px] font-light">Niño/a</div>
                            </div>
                        </div>
                        <span class="text-xs text-red-600 font-medium">* Impuestos no incluidos</span>
                    </div>

                    <!-- ####  Information  #### -->
                    <div class="max-w-[calc(100vw-30px)] mt-8 text-slate-800 border-b pb-4">
                        <Tabs value="0" scrollable>
                            <TabList>
                                <Tab value="0">Información</Tab>
                                <Tab value="1">Marcaje</Tab>
                                <Tab value="2">Packaging</Tab>
                            </TabList>

                            <TabPanels>
                                <TabPanel value="0">
                                    <div id="description" class="w-full h-full px-2 pt-2 text-sm text-slate-600">
                                        <div class="w-full h-full flex flex-col gap-2">
                                            <p class="font-light whitespace-pre-line">
                                                <span class="font-semibold">Material:</span> {{ useTools().addLineBreaks(product?.composition) }}
                                            </p>
                                            <p class="font-light whitespace-pre-line leading-tight">{{ useTools().addLineBreaks(product?.observations) }}</p>
                                        </div>
                                        <div class="flex mt-4">
                                            <NuxtLink target="_blank" :to="`https://static.gorfactory.es/b2b/Ficha_Tecnica/es/pdf/${ product?.modelcode }_es.pdf`">
                                                <Button severity="contrast" variant="outlined" class="w-fit">
                                                    <div class="flex items-center gap-2">
                                                        <Icon name="akar-icons:download" size="16" />
                                                        <span class="font-light">Ficha técnica</span>
                                                    </div>
                                                </Button>
                                            </NuxtLink>
                                        </div>
                                    </div>
                                </TabPanel>
                                <TabPanel value="1">
                                    <div v-if="prints?.length > 0" id="marking" class="w-full h-full px-2 pt-2 flex flex-col gap-2">
                                        <FloatLabel class="w-full my-4" variant="on">
                                            <Select v-model="printSelected" inputId="on_label" :options="prints" option-label="name" option-value="id" class="w-full" />
                                            <label for="on_label">Seleccione el marcaje</label>
                                        </FloatLabel>

                                        <div v-if="printSelected" class="w-full h-full flex flex-col gap-6">
                                            <div class="w-full flex flex-col items-center gap-2">
                                                <h3 class="text-lg font-semibold">{{ prints.find((price: any) => price.id === printSelected)?.name }}</h3>
                                                <div class="bg-ebp-500 w-12 h-1"></div>
                                            </div>

                                            <div class="w-full grid grid-cols-1 md:grid-cols-2 gap-2">
                                                <div v-for="area in printPrices" :key="area.id" class="flex items-center gap-1">
                                                    <NuxtImg 
                                                        :src="area.image" 
                                                        class="w-20 h-20 object-cover" 
                                                        quality="40" format="webp" width="112" height="112" 
                                                        preload placeholder="/images/empty_img.webp"
                                                    />

                                                    <div class="flex flex-col text-xs">
                                                        <div class="font-semibold">{{ area.name }}</div>
                                                        <div>{{ area.description }}</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div v-else class="w-full h-full p-8 flex justify-center items-center">
                                        <AxEmpty message="Sin Marcaje"></AxEmpty>
                                    </div>
                                </TabPanel>
                                <TabPanel value="2">
                                    <div class="w-full h-full px-2 pt-2 text-sm font-light flex flex-col gap-2">
                                        <div class="flex flex-col gap-2 justify-start">
                                            <div>
                                                <span class="font-semibold">Pack: </span>
                                                <span>{{ product?.packunits }} unidad(es),</span>
                                                <span class="font-semibold"> Caja: </span>
                                                <span>{{ product?.boxunits }} unidades </span>
                                            </div>
                                            <div class="flex gap-2">
                                                <span class="font-semibold">Taric:</span> <span>{{ product?.taric }} </span>
                                            </div>
                                        </div>
                                    </div>
                                </TabPanel>
                            </TabPanels>
                        </Tabs>
                    </div>
                </div>
            </div>

            <Divider />

            <div class="flex flex-col mb-[-14px]">
                <div class="text-lg font-semibold text-slate-800">Seleccione cantidad por color</div>
                <div class="bg-ebp-500 w-12 h-1 mt-1 mb-3"></div>
            </div>

            <!-- ####  Sale  #### -->
            <div 
                v-if="colorSelected && variants && variants?.length > 0" 
                id="shop" class="w-full h-full grid md:grid-cols-[1.5fr_1fr] gap-4 md:gap-0 border"
            >
                <div class="w-full px-0 md:px-0 border-r-0 md:border-r">
                    <!-- <div class="w-full p-4 flex flex-wrap gap-2">
                        <div 
                            v-for="color in uniqueColors" :key="color.id"
                            class="border border-white rounded-md p-1 hover:border-slate-300 hover:cursor-pointer hover:shadow-md"
                            :class="{ '!border-slate-300 cursor-pointer shadow-md': color.id === colorSelected.id }"
                            @click="() => colorSelected = color"
                        >
                            <NuxtImg v-tooltip.top="{ value: color.name, showDelay: 400 }"
                                :src="color.productimage" 
                                class="w-20 h-20 object-cover" 
                                quality="40" format="webp" width="200" 
                                preload placeholder="/images/empty_img.webp"
                            />
                        </div>
                    </div> -->

                    <div class="w-full px-4 py-8 border-b">
                        <div class="text-xl font-semibold uppercase text-ebp-800">{{ product?.fullName }}</div>
                        <div class="w-full flex items-center gap-2">
                            <div class="w-fit h-fit">
                                <div class="flex flex-col gap-1 items-center">
                                    <div class="w-8 h-8 border border-slate-300 rounded-full bg-cover bg-center flex justify-center items-center">
                                        <div v-if="colorSelected.hexcode" class="w-full h-full rounded-full" :style="{ backgroundColor: colorSelected.hexcode }"></div>
                                        <div v-else-if="colorSelected.comphexcode" class="w-full h-full rounded-full flex">
                                            <div class="w-1/2 rounded-l-full" :style="{ backgroundColor: colorSelected.comphexcode.split('/')[0] }"></div>
                                            <div class="w-1/2 rounded-r-full" :style="{ backgroundColor: colorSelected.comphexcode.split('/')[1] }"></div>
                                        </div>
                                        <div v-else-if="colorSelected.url" class="w-full h-full rounded-full" :style="{ backgroundImage: `url(${colorSelected.url})` }"></div>
                                    </div>
                                </div>
                            </div>
                            {{ colorSelected.id }} - <span class="font-semibold">{{ colorSelected.name }}</span>
                        </div>
                    </div>

                    <div>
                        <div class="md:grid-cols-5 gap-2 p-2 border-b hidden md:grid">
                            <p class="font-semibold flex justify-center items-center">CANTIDAD</p>
                            <p class="font-semibold flex justify-center items-center">TALLA</p>
                            <p class="font-semibold flex justify-center items-center">PRECIO</p>
                            <p class="font-semibold flex justify-center items-center">STOCK</p>
                            <p class="font-semibold flex justify-center items-center">DISPONIBILIDAD</p>
                        </div>

                        <div
                            v-for="variant in getVariantsByColor(colorSelected.id)" :key="variant.id"
                            class="grid md:grid-cols-5 gap-2 border-b text-sm odd:bg-gray-100 even:bg-white"
                            :class="[{ 'p-2': variant.onhand > 0 || variant.canteco > 0 }, { 'py-4': variant.onhand === 0 }]"
                        >
                            <!-- ####  Rows  #### -->
                            <div class="flex gap-2 justify-between md:justify-center items-center">
                                <p class="md:hidden font-semibold">Cantidad:</p>
                                <div v-if="variant.onhand > 0" class="w-full px-2 md:p-0">
                                    <InputNumber 
                                        v-model="purchaseQuantity[variant.id].quantity" 
                                        showButtons 
                                        fluid
                                        buttonLayout="horizontal" 
                                        :step="1" 
                                        size="small"
                                        class="text-center"
                                        :min="0" :max="variant.onhand"
                                        :locale="'es'"
                                        :useGrouping="false"
                                    >
                                        <template #incrementicon>
                                            <Icon name="akar-icons:plus" />
                                        </template>
                                        <template #decrementicon>
                                            <Icon name="akar-icons:minus" />
                                        </template>
                                    </InputNumber>
                                </div>
                            </div>
                            <div class="flex gap-1 justify-start md:justify-center items-center">
                                <p class="md:hidden font-semibold">Talla:</p>
                                <div class="flex flex-col items-center">
                                    <span v-if="variant.isforchildren" class="text-[9px]">niño/a</span> <span>{{ variant.size.name }}</span>
                                </div>
                            </div>
                            <div class="flex gap-1 justify-start md:justify-center items-center">
                                <p class="md:hidden font-semibold">Precio:</p>
                                <span>{{ variant.price_box_pvp }} €</span>
                            </div>
                            <div class="flex gap-1 justify-start md:justify-center items-center">
                                <p class="md:hidden font-semibold">Stock:</p>
                                <p>{{ variant.onhand }}</p>
                            </div>
                            <div class="flex gap-1 justify-start md:justify-center items-center">
                                <p class="md:hidden font-semibold">Disponibilidad:</p>
                                <p v-if="variant.onhand > 0">En stock</p>
                                <p v-else>{{ variant.incoming }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="p-4 flex flex-col gap-8">
                    <div v-if="totals.units > 0 && prints?.length > 0" class="flex flex-col justify-start gap-4">
                        <div class="flex items-center gap-3 border rounded-md px-4 py-2">
                            <ToggleSwitch v-model="switchMarking" @change="() => { areaSelected = null; switchClicheRep = false; marking = []; }" />
                            <span class="font-semibold">¿Incluir Marcaje?</span>
                        </div>

                        <div v-show="switchMarking" class="flex flex-col gap-4 transition-all duration-300 ease-in-out">
                            <div class="flex items-center gap-3 border rounded-md px-4 py-2">
                                <ToggleSwitch v-model="switchClicheRep" @change="() => { areaSelected = null; }" class="!min-w-[48px]" />
                                <span class="font-semibold">¿Repiten trabajos? (Repetir Cliché)</span>
                            </div>

                            <div class="flex flex-col gap-2 border rounded-md p-2">
                                <div class="w-full font-semibold text-center">Seleccione el marcaje, área y cantidad de colores que desea.</div>
                                <Select v-model="printSelected" :options="prints" option-label="name" option-value="id" placeholder="Seleccione" @change="() => areaSelected = null" />
                                <Select v-model="areaSelected" :options="prints.find((price: any) => price.id === printSelected)?.prices" option-label="name" option-value="id" placeholder="Seleccione" />
                            </div>

                            <Button v-if="areaSelected !== null" @click="() => { addMarking(printSelected, areaSelected); areaSelected = null; }">Agregar Marcaje</Button>
                        </div>

                        <Divider />
                    </div>

                    <div>
                        <div class="flex justify-between text-2xl font-semibold">
                            <div>Total:</div>
                            <div>{{ totals.fullTotal.toFixed(2) }} €</div>
                        </div>

                        <div class="flex justify-between text-base">
                            <div>Precio Unitario:</div>
                            <div>{{ totals.unitary.toFixed(2) }} €</div>
                        </div>

                        <div class="flex justify-between text-base">
                            <div>Unidades:</div>
                            <div>{{ totals.units }}</div>
                        </div>

                        <Divider />

                        <div class="">
                            <div v-for="(PurchasedVariant, id, i) in onlyPurchaseQuantity" :key="id" class="pb-4 border-b" :class="{ 'pt-4': (i > 0) }">
                                <div class="font-semibold">{{ PurchasedVariant.product }}</div>
                                <div class="text-xs">{{ id }} - {{ PurchasedVariant.variant.itemname }}</div>
                                <div class="text-xs">{{ PurchasedVariant.quantity }} unidades x {{ PurchasedVariant.variant.price_box_pvp }} €</div>
                            </div>

                            <div v-for="(mark) in marking" :key="`${ mark.print.id }-${ mark.price.id }`">
                                <div class="pb-4 pt-4 border-b">
                                    <div class="flex w-full">
                                        <div class="w-2/3">
                                            <div class="font-semibold">{{ mark.print.name }}</div>
                                            <div class="font-semibold flex justify-left items-center gap-1">
                                                <span>{{ mark.marking.description }}</span><span v-if="mark.clicheRep">-</span>
                                                <span v-if="mark.clicheRep" class="text-xs font-semibold text-orange-600"> REPETICIÓN</span>
                                            </div>
                                            <div class="text-xs">{{ mark.marking.complement }}</div>
                                            <div v-if="mark.price?.type" class="text-xs">{{ totals.units }} unidades x {{ (getCurrentPrice(mark.price.type) / 100).toFixed(2) }} €</div>
                                            <div v-if="mark.price?.cliche > 0 && !mark.clicheRep" class="text-xs">Picaje - 1 x {{ mark.price.cliche / 100 }} €</div>
                                        </div>
                                        <div class="w-1/3 flex justify-end items-center">
                                            <Button text @click="deleteLineOfMarking(mark.print.id, mark.price.id)">
                                                <Icon name="ic:baseline-delete-outline" class="text-red-600" size="25" />
                                            </Button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div v-if="currentMarkingPrices.repetitions[slug].repetition" class="py-4 border-b">
                                <div class="w-full flex flex-col">
                                    <div class="font-semibold"> Puesta en máquina </div>
                                    <div class="text-xs"> 1 x {{ (currentMarkingPrices.repetitions[slug].price / 100).toFixed(2) }} € </div>
                                </div>
                            </div>
                        </div>

                        <div v-if="totals.units > 0 && totals.units < product.moq" class="w-full mt-4">
                            <Message severity="warn">
                                <span class="font-light">La orden mínima para este producto es de {{ product.order_min_product }} unidades.</span>
                            </Message>
                        </div>
                        <div v-else-if="totals.units > 0" class="w-full mt-4 flex justify-end">
                            <Button :severity="itemId ? 'info' : 'success'" @click="addCart"><span class="font-semibold">{{ itemId ? 'Actualizar Carrito' : 'Agregar al Carrito' }}</span></Button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- ####  Loading Skeleton  #### -->
        <div v-else class="container w-full h-full mx-auto px-4 py-8 flex flex-col gap-6">
            <div>
                <div class="w-full h-full grid md:grid-cols-2 gap-6">
                    <div class="w-full !max-w-[612px] h-full flex flex-col gap-4">
                        <div class="w-full !max-w-[612px] h-[300px] md:h-[500px] max-h-[612px] flex justify-center items-start">
                            <Skeleton height="500px" width="100%" class="hidden md:block" />
                            <Skeleton height="300px" width="100%" class="md:hidden" />
                        </div>

                        <div class="!h-[116px] max-!h-[116px]" content-class="max-w-scroll md:w-full !h-full flex items-center gap-1">
                            <Skeleton height="116px" width="100%" class="w-full h-full object-cover" />
                        </div>
                    </div>

                    <div class="w-full flex flex-col gap-2">
                        <div class="w-full h-24">
                            <Skeleton height="96px" width="100%" class="w-full h-full object-cover" />
                        </div>

                        <div class="w-full flex flex-col gap-2">
                            <div>
                                <Skeleton height="24px" width="96px" />
                            </div>
                            <div>
                                <div>
                                    <Skeleton height="34px" width="300px" />
                                </div>
                                <div class="bg-slate-300 w-12 h-0.5 my-5"></div>
                            </div>

                            <div class="flex gap-2">
                                <Skeleton height="20px" width="80px" />
                            </div>
                            <div class="flex gap-2">
                                <Skeleton height="20px" width="85%" />
                            </div>
                            <div class="flex gap-2">
                                <Skeleton height="20px" width="80%" />
                            </div>

                            <div class="flex flex-wrap gap-2">
                                <Skeleton height="20px" width="120px" />
                                <Skeleton height="20px" width="125px" />
                                <Skeleton height="20px" width="120px" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
