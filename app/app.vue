<script setup lang="ts">
  const {
    cookiesEnabledIds
  } = useCookieControl()

  watch(
    () => cookiesEnabledIds.value,
    (current, previous) => {
      if (
        !previous?.includes('google-analytics') &&
        current?.includes('google-analytics')
      ) {
        window.location.reload()
      }
    },
    { deep: true },
  )
</script>

<template>
  <div>
    <CookieControl locale="es">
      <template #modal>
        <h2>Gestionar Cookies</h2>
        <p>Selecciona tus preferencias de cookies:</p>
      </template>
    </CookieControl>

    <NuxtLayout>
      <NuxtPage />
    </NuxtLayout>
  </div>
</template>

<style>
  button[data-v-inspector="node_modules/@dargmuesli/nuxt-cookie-control/dist/runtime/components/CookieControl.vue:28:15"] {
    display: none !important;
  }
</style>
