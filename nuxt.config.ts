import { defineOrganization } from 'nuxt-schema-org/schema';

export default defineNuxtConfig({
  compatibilityDate: '2024-11-01',
  future: { compatibilityVersion: 4 },
  devtools: { enabled: true },
  modules: [
    'nuxt-auth-utils',
    '@nuxtjs/i18n',
    'nuxt-nodemailer',
    '@pinia/nuxt',
    'pinia-plugin-persistedstate/nuxt',
    '@nuxtjs/tailwindcss',
    '@nuxtjs/google-fonts',
    '@nuxt/icon',
    '@nuxt/image',
    '@nuxtjs/seo',
    'nuxt-gtag',
    '@dargmuesli/nuxt-cookie-control',
    '@primevue/nuxt-module'
  ],

  css: ["~/assets/css/main.css"],
  postcss: {
    plugins: {
      'postcss-import': {},
      tailwindcss: {},
      autoprefixer: {},
    },
  },
  tailwindcss: {
    config: {
      theme: {
        extend: {
          colors: {
            primary: {
              DEFAULT: "#9FB838",
              50:  "#F7FBE6",
              100: "#EAF4B8",
              200: "#DFEE96",
              300: "#D4E974",
              400: "#C9E252",
              500: "#BAD543",
              600: "#9FB838",
              700: "#82942D",
              800: "#657122",
              900: "#484D17",
              950: "#2E310D",
            },
            ebp: {
              DEFAULT: "#9FB838",
              50:  "#F7FBE6",
              100: "#EAF4B8",
              200: "#DFEE96",
              300: "#D4E974",
              400: "#C9E252",
              500: "#BAD543",
              600: "#9FB838",
              700: "#82942D",
              800: "#657122",
              900: "#484D17",
              950: "#2E310D",
            },
            active: '#9FB838'
          }
        },
      },
    },
  },
  primevue: {
    importTheme: { from: '@/assets/themes/config.js' },
  },
  i18n: {
    locales: [
      { code: 'es', language: 'es-ES', file: 'es.json' },
    ],
    defaultLocale: 'es',
    lazy: true,
    strategy: 'no_prefix'
  },
  googleFonts: {
    families: {
      Poppins: [200, 300, 400, 500, 600, 700, 800],
    },
  },
  image: {
    format: ['webp'],
    provider: 'ipx',
    domains: ['static.gorfactory.es']
  },
  cookieControl: {
    locales: ['es'],
    isControlButtonEnabled: false
  },

  site: {
    url: 'https://ebppublicidad.es',
    name: 'EBP Publicidad',
    title: 'EBP Publicidad - Personalización Textil, Serigrafía, Bordado y Estampado',
    description: 'Especialistas en personalización textil con serigrafía, bordado y estampado. Ofrecemos camisetas, polos y sudaderas personalizadas con diseños de alta calidad. ¡Haz tu marca visible con EBP Publicidad!',
    defaultLocale: 'es',
  },
  schemaOrg: {
    identity: defineOrganization({
      '@type': ['Organization', 'Store', 'OnlineStore'],
      'name': 'EBP Publicidad',
      'logo': '/images/logo.webp',
    }),
  },
  seo: {
    meta: {
      title: 'EBP Publicidad - Personalización Textil, Serigrafía, Bordado y Estampado',
      description: 'Especialistas en personalización textil con serigrafía, bordado y estampado. Ofrecemos camisetas, polos y sudaderas personalizadas con diseños de alta calidad. ¡Haz tu marca visible con EBP Publicidad!',
    },
  },
  app: {
    head: {
      charset: 'utf-8',
      templateParams: {
        separator: '|',
      },
    },
  },
  sitemap: {
    autoI18n: false,
    includeAppSources: true,
    exclude: ['/auth/**', '/profile/**'],
    sources: [`/api/ebp/store/product`],
    urls: async () => {
      const languages = ['esp'];
      return languages.map(lang => ({
        loc: `/catalogo`,
        changefreq: 'daily',
        priority: 0.8
      }));
    }
  },
  gtag: {
    id: process.env.GTAG_ID || '',
    enabled: process.env.NODE_ENV === 'production'
  },

  piniaPluginPersistedstate: {
    storage: 'localStorage',
  },

  nodemailer: {
    from: '"EBP Publicidad" <<EMAIL>>',
    host: process.env.EMAIL_HOST || 'sandbox.smtp.mailtrap.io',
    port: parseInt(process.env.EMAIL_PORT || '465'),
    secure: (process.env.EMAIL_SECURE === 'true'),
    auth: {
      user: process.env.EMAIL_USER || 'f8c63829ef1bd6',
      pass: process.env.EMAIL_PASS || '711bda6c74fe30',
    }
  },

  runtimeConfig: {
    public: {
      site: {
        url: process.env.NUXT_SITE_URL || 'http://localhost:3001'
      },
      paypal: {
        clientId: process.env.PAYPAL_CLIENT_ID || ''
      }
    },
    paypal: {
      clientId: process.env.PAYPAL_CLIENT_ID || '',
      clientSecret: process.env.PAYPAL_CLIENT_SECRET || '',
      mode: process.env.PAYPAL_MODE || 'sandbox',
    },
    data: {
      origin: process.env.DATA_ORIGIN || 'prisma'
    },
    hasura: {
      url: process.env.HASURA_URL || 'http://localhost:8080/v1/graphql',
      debug: (process.env.HASURA_DEBUG === 'true') || false,
      verifyKey: process.env.HASURA_VERIFY_KEY || 'verify-key',
    },
    session: {
      name: 'arpix_roly_auth_session',
      maxAge: 60 * 60 * 24 * 7 // 1 week
    },
    auth: {
      register: {
        verifyRequired: (process.env.AUTH_REGISTER_VERIFY_REQUIRED === 'true') || false
      }
    },
    site: {
      url: process.env.NUXT_SITE_URL || 'http://localhost:3001'
    },
    redis: {
      host: process.env.REDIS_HOST,
      port: parseInt(process.env.REDIS_PORT || '6379'),
      password: process.env.NODE_ENV === 'production' ? process.env.REDIS_PASSWORD : undefined
    },
    roly: {
      login: process.env.ROLY_LOGIN,
      catalog: process.env.ROLY_CATALOG,
      category: process.env.ROLY_CATEGORY,
      stock: process.env.ROLY_STOCK,
      price: process.env.ROLY_PRICE,
      user: process.env.ROLY_USER,
      password: process.env.ROLY_PASSWORD,
      url: process.env.ROLY_URL,
    }
  }
});
